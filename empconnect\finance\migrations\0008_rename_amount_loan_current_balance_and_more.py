# Generated by Django 5.0.3 on 2025-07-23 02:48

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('finance', '0007_remove_ojtpayslipdata_line_number'),
    ]

    operations = [
        migrations.RenameField(
            model_name='loan',
            old_name='amount',
            new_name='current_balance',
        ),
        migrations.RenameField(
            model_name='loan',
            old_name='balance',
            new_name='principal_amount',
        ),
        migrations.RemoveField(
            model_name='loantype',
            name='updated_at',
        ),
        migrations.AddField(
            model_name='loan',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='loan',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='loantype',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='loantype',
            name='is_stackable',
            field=models.<PERSON>oleanField(default=True),
        ),
        migrations.AlterField(
            model_name='loan',
            name='monthly_deduction',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name='loantype',
            name='loan_type',
            field=models.CharField(max_length=100, unique=True),
        ),
        migrations.CreateModel(
            name='LoanDeduction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cut_off', models.CharField(max_length=50)),
                ('balance_before', models.DecimalField(decimal_places=2, max_digits=10)),
                ('balance_after', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deductions', to='finance.loan')),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('loan', 'cut_off')},
            },
        ),
    ]
