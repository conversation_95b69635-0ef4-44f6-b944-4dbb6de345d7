/**
 * Leave Management - User Interface JavaScript
 * Handles employee and approver functionalities
 */

class LeaveUserInterface {
    constructor() {
        this.currentModal = null;
        this.currentDate = new Date();
        this.leaveData = [];
        this.holidays = [];
        this.sundayExceptions = [];
        this.init();
    }

    init() {
        this.loadHolidaysAndExceptions();
        this.bindEvents();
        this.initializeDateInputs();
        this.initializeTabs();
        this.setupFormValidation();
        this.initializeCalendar();
        this.initializeCharts();
        this.initializeHoursEditor();
    }

    async loadHolidaysAndExceptions() {
        try {
            const response = await fetch('/leave/api/holidays-and-exceptions/');
            const data = await response.json();
            this.holidays = data.holidays || [];
            this.sundayExceptions = data.sunday_exceptions || [];
        } catch (error) {
            console.error('Error loading holidays and exceptions:', error);
            this.holidays = [];
            this.sundayExceptions = [];
        }
    }

    bindEvents() {
        // Event delegation for all actions
        document.addEventListener('click', this.handleClick.bind(this));
        document.addEventListener('change', this.handleChange.bind(this));
        document.addEventListener('submit', this.handleSubmit.bind(this));

        // Modal overlay clicks
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeCurrentModal();
            }
        });

        // Keyboard events
        document.addEventListener('keydown', this.handleKeyboard.bind(this));
    }

    handleClick(e) {
        // Always get the closest element with data-action, so clicking <i> inside button works
        const actionEl = e.target.closest('[data-action]');
        if (!actionEl) return;
        const action = actionEl.getAttribute('data-action');
        e.preventDefault();
        switch (action) {
            case 'apply-leave':
                this.openApplyLeaveModal();
                break;
            case 'view-details':
                this.viewLeaveDetails(actionEl);
                break;
            case 'cancel-leave':
                this.cancelLeave(actionEl);
                break;
            case 'open-approval':
                this.openApprovalModal(actionEl);
                break;
            case 'process-approval':
                this.processApproval(actionEl);
                break;
            case 'scroll-to-balances':
                this.scrollToBalances();
                break;
            case 'refresh-data':
                this.refreshData();
                break;
            case 'close-modal':
                this.closeModal(actionEl.getAttribute('data-modal'));
                break;
        }
    }

    handleChange(e) {
        const element = e.target;
        
        if (element.id === 'id_leave_type') {
            this.checkLeaveBalance();
        }

        if (element.id === 'id_date_from') {
            // Set min for Date To
            const dateTo = document.getElementById('id_date_to');
            if (dateTo) {
                dateTo.min = element.value;
                // If Date To is before Date From, reset it
                if (dateTo.value && dateTo.value < element.value) {
                    dateTo.value = element.value;
                }
            }
        }

        if (element.id === 'id_date_from' || element.id === 'id_date_to') {
            this.calculateDays();
            this.validateDates();
        }
    }

    handleSubmit(e) {
        if (e.target.id === 'applyLeaveForm') {
            if (!this.validateLeaveForm()) {
                e.preventDefault();
                return false;
            }
            this.showFormSubmitting(e.target);
        }
    }

    handleKeyboard(e) {
        // Escape key closes modals
        if (e.key === 'Escape' && this.currentModal) {
            this.closeCurrentModal();
        }
        
        // Ctrl/Cmd + N opens apply leave modal
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            this.openApplyLeaveModal();
        }
    }

    initializeDateInputs() {
        const today = new Date().toISOString().split('T')[0];
        const dateInputs = document.querySelectorAll('#applyLeaveModal input[type="date"]');
        
        dateInputs.forEach(input => {
            input.min = today;
        });
    }

    initializeTabs() {
        const tabs = document.querySelectorAll('.tab');
        const tabPanels = document.querySelectorAll('.tab-panel');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const target = tab.getAttribute('data-target');
                this.switchTab(target);
            });
        });
    }

    switchTab(targetId) {
        // Remove active from all tabs and panels
        document.querySelectorAll('.tab').forEach(tab => {
            tab.classList.remove('active');
        });
        
        document.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.remove('active');
        });

        // Activate selected tab and panel
        const activeTab = document.querySelector(`[data-target="${targetId}"]`);
        const activePanel = document.getElementById(targetId);
        
        if (activeTab) activeTab.classList.add('active');
        if (activePanel) {
            activePanel.classList.add('active');
            this.onTabActivated(targetId);
        }
    }

    onTabActivated(tabId) {
        switch (tabId) {
            case 'my-requests':
                this.refreshMyRequests();
                break;
            case 'approvals':
                this.refreshApprovals();
                break;
        }
    }

    // Leave Application Functions
    openApplyLeaveModal() {
        this.resetApplyForm();
        this.openModal('applyLeaveModal');
    }

    resetApplyForm() {
        const form = document.getElementById('applyLeaveForm');
        if (form) {
            form.reset();
            
            // Reset displays
            const balanceDisplay = document.getElementById('balanceDisplay');
            const daysCountElement = document.getElementById('daysCount');
            const daysRangeElement = document.getElementById('daysRange');
            const hrsCountElement = document.getElementById('hrsCount');
            const hrsRangeElement = document.getElementById('hrsRange');
            const daysDisplay = document.getElementById('daysRequested');
            const daysFormRow = daysDisplay ? daysDisplay.closest('.form-row') : null;
            
            if (balanceDisplay) {
                balanceDisplay.innerHTML = '<span class="balance-text">Select leave type to view balance</span>';
            }
            
            if (daysCountElement) {
                daysCountElement.textContent = 'Select dates to calculate';
                daysCountElement.className = 'days-count';
            }
            
            if (daysRangeElement) {
                daysRangeElement.textContent = '';
            }
            
            if (hrsCountElement) {
                hrsCountElement.textContent = 'Hours Count';
                hrsCountElement.className = 'days-count';
            }
            
            if (hrsRangeElement) {
                hrsRangeElement.textContent = this.getHoursRange();
            }
            
            // Hide the days display
            if (daysFormRow) {
                daysFormRow.classList.add('days-hidden');
                daysFormRow.classList.remove('days-show');
            }
            
            // Reset hours editor state
            this.isEditingHours = false;
            this.canEditHours = false;
            if (this.hoursEditButton) {
                this.hoursEditButton.style.display = 'none';
            }
        }
    }

    async checkLeaveBalance() {
        const leaveTypeSelect = document.getElementById('id_leave_type');
        const balanceDisplay = document.getElementById('balanceDisplay');
        
        if (!leaveTypeSelect || !balanceDisplay) return;
        
        const leaveTypeId = leaveTypeSelect.value;
        
        if (!leaveTypeId) {
            balanceDisplay.innerHTML = '<span class="balance-text">Select leave type to view balance</span>';
            return;
        }

        balanceDisplay.innerHTML = '<span class="balance-text"><i class="fas fa-spinner fa-spin"></i> Loading balance...</span>';

        try {
            const response = await fetch(`/leave/ajax/balance/?leave_type_id=${leaveTypeId}`);
            const data = await response.json();
            
            if (data.remaining !== null) {
                balanceDisplay.innerHTML = `
                    <div class="balance-info">
                        <span class="balance-remaining">${data.remaining} days remaining</span>
                        <small class="balance-period">${data.valid_period}</small>
                    </div>
                `;
            } else {
                balanceDisplay.innerHTML = '<span class="balance-text text-muted">No active balance found</span>';
            }
        } catch (error) {
            console.error('Error fetching balance:', error);
            balanceDisplay.innerHTML = '<span class="balance-text text-error">Error loading balance</span>';
        }
    }

    calculateDays() {
        const dateFromInput = document.getElementById('id_date_from');
        const dateToInput = document.getElementById('id_date_to');
        const daysCountElement = document.getElementById('daysCount');
        const daysRangeElement = document.getElementById('daysRange');
        const hrsCountElement = document.getElementById('hrsCount');
        const hrsRangeElement = document.getElementById('hrsRange');
        const daysDisplay = document.getElementById('daysRequested');
        const daysFormRow = daysDisplay ? daysDisplay.closest('.form-row') : null;
        
        if (!dateFromInput || !dateToInput || !daysCountElement || !daysRangeElement || !hrsCountElement || !hrsRangeElement || !daysDisplay || !daysFormRow) return;
        
        const dateFrom = dateFromInput.value;
        const dateTo = dateToInput.value;
        
        if (!dateFrom || !dateTo) {
            // Hide the days display with animation
            daysFormRow.classList.add('days-hidden');
            daysFormRow.classList.remove('days-show');
            
            daysCountElement.textContent = 'Select dates to calculate';
            daysRangeElement.textContent = '';
            hrsCountElement.textContent = 'Hours Count';
            hrsRangeElement.textContent = '';
            return;
        }
        
        const fromDate = new Date(dateFrom);
        const toDate = new Date(dateTo);
        
        if (toDate < fromDate) {
            daysCountElement.textContent = 'End date cannot be before start date';
            daysCountElement.className = 'days-count text-error';
            daysRangeElement.textContent = '';
            hrsCountElement.textContent = 'Hours Count';
            hrsRangeElement.textContent = '';
            return;
        }
        
        // Calculate working days excluding Sundays (unless in exceptions) and holidays
        let workingDays = 0;
        const currentDate = new Date(fromDate);
        
        while (currentDate <= toDate) {
            const dateStr = currentDate.toISOString().split('T')[0];
            const isSunday = currentDate.getDay() === 0;
            const isHoliday = this.holidays.includes(dateStr);
            const isSundayException = this.sundayExceptions.includes(dateStr);
            
            // Count the day if:
            // - It's not a Sunday, OR it's a Sunday but in exceptions
            // - AND it's not a holiday
            if ((!isSunday || isSundayException) && !isHoliday) {
                workingDays++;
            }
            
            currentDate.setDate(currentDate.getDate() + 1);
        }
        
        // Calculate hours (8 hours per day instead of 9)
        const hoursPerDay = workingDays === 1 ? this.getHoursPerDay() : 8;
        const totalHours = workingDays * hoursPerDay;
        
        daysCountElement.textContent = `${workingDays} working day${workingDays !== 1 ? 's' : ''}`;
        daysCountElement.className = 'days-count';
        daysRangeElement.textContent = `${this.formatDate(fromDate)} to ${this.formatDate(toDate)}`;
        
        hrsCountElement.textContent = `${totalHours} hour${totalHours !== 1 ? 's' : ''}`;
        hrsCountElement.className = 'days-count';
        hrsRangeElement.textContent = workingDays === 1 ? this.getHoursRange() : '8:00 AM to 5:00 PM';
        
        // Show the days display with animation
        daysFormRow.classList.remove('days-hidden');
        daysFormRow.classList.add('days-show');
        
        // Show/hide edit button based on day count
        this.toggleHoursEditButton(workingDays === 1);
    }

    getHoursPerDay() {
        // Get stored time range or use default (8:00 AM to 5:00 PM = 8 hours)
        const timeFrom = localStorage.getItem('leaveTimeFrom') || '08:00';
        const timeTo = localStorage.getItem('leaveTimeTo') || '17:00';
        
        const [fromHour, fromMin] = timeFrom.split(':').map(Number);
        const [toHour, toMin] = timeTo.split(':').map(Number);
        
        const fromMinutes = fromHour * 60 + fromMin;
        const toMinutes = toHour * 60 + toMin;
        
        return (toMinutes - fromMinutes) / 60;
    }

    getHoursRange() {
        const timeFrom = localStorage.getItem('leaveTimeFrom') || '08:00';
        const timeTo = localStorage.getItem('leaveTimeTo') || '17:00';
        
        // Convert to 12-hour format
        const formatTime = (time) => {
            const [hour, minute] = time.split(':').map(Number);
            const ampm = hour >= 12 ? 'PM' : 'AM';
            const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
            return `${displayHour}:${minute.toString().padStart(2, '0')} ${ampm}`;
        };
        
        return `${formatTime(timeFrom)} to ${formatTime(timeTo)}`;
    }

    initializeHoursEditor() {
        const hrsCountElement = document.getElementById('hrsCount');
        if (!hrsCountElement) return;

        // Create edit button
        const editButton = document.createElement('button');
        editButton.type = 'button';
        editButton.className = 'hours-edit-btn';
        editButton.innerHTML = '<i class="fas fa-edit"></i>';
        editButton.title = 'Edit hours';
        editButton.style.display = 'none';

        // Add edit button to the hrs count element
        const hrsInfoDiv = hrsCountElement.closest('.days-info');
        if (hrsInfoDiv) {
            hrsInfoDiv.style.position = 'relative';
            hrsInfoDiv.appendChild(editButton);
        }

        // Show/hide edit button on hover (only when allowed)
        hrsInfoDiv.addEventListener('mouseenter', () => {
            if (this.canEditHours) {
                editButton.style.display = 'block';
            }
        });

        hrsInfoDiv.addEventListener('mouseleave', () => {
            if (!this.isEditingHours) {
                editButton.style.display = 'none';
            }
        });

        // Handle edit button click
        editButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (this.canEditHours) {
                this.showHoursEditor();
            }
        });

        // Store reference to edit button
        this.hoursEditButton = editButton;
    }

    toggleHoursEditButton(canEdit) {
        this.canEditHours = canEdit;
        if (this.hoursEditButton) {
            if (!canEdit) {
                this.hoursEditButton.style.display = 'none';
            }
        }
    }

    showHoursEditor() {
        this.isEditingHours = true;
        const hrsRangeElement = document.getElementById('hrsRange');
        if (!hrsRangeElement) return;

        const currentTimeFrom = localStorage.getItem('leaveTimeFrom') || '08:00';
        const currentTimeTo = localStorage.getItem('leaveTimeTo') || '17:00';

        // Create editor HTML
        const editorHTML = `
            <div class="hours-editor">
                <div class="hours-inputs">
                    <div class="time-input-group">
                        <label>From:</label>
                        <input type="time" id="timeFromInput" value="${currentTimeFrom}" class="time-input">
                    </div>
                    <div class="time-input-group">
                        <label>To:</label>
                        <input type="time" id="timeToInput" value="${currentTimeTo}" class="time-input">
                    </div>
                </div>
                <div class="hours-editor-actions">
                    <button type="button" class="btn-save-hours">Save</button>
                    <button type="button" class="btn-cancel-hours">Cancel</button>
                </div>
            </div>
        `;

        hrsRangeElement.innerHTML = editorHTML;

        // Add event listeners
        const saveBtn = hrsRangeElement.querySelector('.btn-save-hours');
        const cancelBtn = hrsRangeElement.querySelector('.btn-cancel-hours');

        saveBtn.addEventListener('click', () => this.saveHoursEdit());
        cancelBtn.addEventListener('click', () => this.cancelHoursEdit());

        // Focus on first input
        const timeFromInput = document.getElementById('timeFromInput');
        if (timeFromInput) {
            timeFromInput.focus();
        }
    }

    saveHoursEdit() {
        const timeFromInput = document.getElementById('timeFromInput');
        const timeToInput = document.getElementById('timeToInput');

        if (!timeFromInput || !timeToInput) return;

        const timeFrom = timeFromInput.value;
        const timeTo = timeToInput.value;

        if (!timeFrom || !timeTo) {
            alert('Please select both time from and time to');
            return;
        }

        // Validate time range
        const [fromHour, fromMin] = timeFrom.split(':').map(Number);
        const [toHour, toMin] = timeTo.split(':').map(Number);
        
        const fromMinutes = fromHour * 60 + fromMin;
        const toMinutes = toHour * 60 + toMin;

        if (toMinutes <= fromMinutes) {
            alert('End time must be after start time');
            return;
        }

        // Save to localStorage
        localStorage.setItem('leaveTimeFrom', timeFrom);
        localStorage.setItem('leaveTimeTo', timeTo);

        this.cancelHoursEdit();
        this.calculateDays(); // Recalculate with new hours
    }

    cancelHoursEdit() {
        this.isEditingHours = false;
        const hrsRangeElement = document.getElementById('hrsRange');
        if (hrsRangeElement) {
            hrsRangeElement.textContent = this.getHoursRange();
        }

        // Hide edit button
        const editButton = document.querySelector('.hours-edit-btn');
        if (editButton) {
            editButton.style.display = 'none';
        }
    }

    validateDates() {
        const dateFromInput = document.getElementById('id_date_from');
        const dateToInput = document.getElementById('id_date_to');
        
        if (!dateFromInput || !dateToInput) return;
        
        const dateFrom = dateFromInput.value;
        const dateTo = dateToInput.value;
        
        if (dateFrom && dateTo) {
            dateToInput.min = dateFrom;
            
            const fromDate = new Date(dateFrom);
            const toDate = new Date(dateTo);
            
            if (toDate < fromDate) {
                dateToInput.setCustomValidity('End date cannot be before start date');
            } else {
                dateToInput.setCustomValidity('');
            }
        }
    }

    validateLeaveForm() {
        const leaveType = document.getElementById('id_leave_type').value;
        const dateFrom = document.getElementById('id_date_from').value;
        const dateTo = document.getElementById('id_date_to').value;
        const reason = document.getElementById('id_reason').value.trim();

        if (!leaveType) {
            this.showToast('Please select a leave type', 'error');
            return false;
        }

        if (!dateFrom || !dateTo) {
            this.showToast('Please select both start and end dates', 'error');
            return false;
        }

        if (!reason) {
            this.showToast('Please provide a reason for your leave', 'error');
            return false;
        }

        if (new Date(dateTo) < new Date(dateFrom)) {
            this.showToast('End date cannot be before start date', 'error');
            return false;
        }

        return this.validateFileUpload();
    }

    validateFileUpload() {
        const fileInput = document.getElementById('id_attachment');
        if (!fileInput || fileInput.files.length === 0) return true;
        
        const file = fileInput.files[0];
        const maxSize = 5 * 1024 * 1024; // 5MB
        
        if (file.size > maxSize) {
            this.showToast('File size must be less than 5MB', 'error');
            return false;
        }

        const allowedTypes = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            this.showToast('Invalid file type. Allowed: PDF, DOC, DOCX, JPG, PNG', 'error');
            return false;
        }

        return true;
    }

    setupFormValidation() {
        const form = document.getElementById('applyLeaveForm');
        if (!form) return;

        // Real-time validation
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
        });
    }

    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';

        switch (field.id) {
            case 'id_leave_type':
                if (!value) {
                    isValid = false;
                    message = 'Please select a leave type';
                }
                break;
            case 'id_date_from':
            case 'id_date_to':
                if (!value) {
                    isValid = false;
                    message = 'Please select a date';
                }
                break;
            case 'id_reason':
                if (!value) {
                    isValid = false;
                    message = 'Please provide a reason';
                } else if (value.length < 10) {
                    isValid = false;
                    message = 'Reason should be at least 10 characters';
                }
                break;
        }

        this.setFieldValidation(field, isValid, message);
        return isValid;
    }

    setFieldValidation(field, isValid, message) {
        const parent = field.closest('.form-group');
        if (!parent) return;

        // Remove existing validation
        parent.classList.remove('field-valid', 'field-invalid');
        const existingMessage = parent.querySelector('.validation-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        if (!isValid && message) {
            parent.classList.add('field-invalid');
            const messageElement = document.createElement('small');
            messageElement.className = 'validation-message text-error';
            messageElement.textContent = message;
            parent.appendChild(messageElement);
        } else if (field.value.trim()) {
            parent.classList.add('field-valid');
        }
    }

    showFormSubmitting(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
        }
    }

    // Leave Details and Actions
    async viewLeaveDetails(element) {
        const controlNumber = element.getAttribute('data-control-number') ||
                            element.closest('[data-control-number]')?.getAttribute('data-control-number');
        
        if (!controlNumber) return;

        try {
            const response = await fetch(`/leave/detail/${controlNumber}/`);
            if (!response.ok) throw new Error('Failed to fetch details');
            
            const html = await response.text();
            document.getElementById('leaveDetailsContent').innerHTML = html;
            this.openModal('leaveDetailsModal');
        } catch (error) {
            console.error('Error loading leave details:', error);
            this.showToast('Error loading leave details', 'error');
        }
    }

    async cancelLeave(element) {
        const controlNumber = element.getAttribute('data-control-number') ||
                            element.closest('[data-control-number]')?.getAttribute('data-control-number');
        
        if (!controlNumber) return;

        if (!confirm('Are you sure you want to cancel this leave request?')) {
            return;
        }

        try {
            const response = await fetch(`/leave/cancel/${controlNumber}/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCookie('csrftoken'),
                    'Content-Type': 'application/json',
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showToast(data.message, 'success');
                setTimeout(() => window.location.reload(), 1500);
            } else {
                this.showToast(data.message, 'error');
            }
        } catch (error) {
            console.error('Error cancelling leave:', error);
            this.showToast('Error cancelling leave request', 'error');
        }
    }

    // Approval Functions
    async openApprovalModal(element) {
        const controlNumber = element.getAttribute('data-control-number') ||
                            element.closest('[data-control-number]')?.getAttribute('data-control-number');
        
        if (!controlNumber) return;

        try {
            const response = await fetch(`/leave/detail/${controlNumber}/`);
            if (!response.ok) throw new Error('Failed to fetch details');
            
            const html = await response.text();
            const approvalContent = `
                ${html}
                <div class="approval-form">
                    <div class="form-group">
                        <label for="approvalComments">Comments (Optional)</label>
                        <textarea id="approvalComments" class="form-control" rows="3" placeholder="Add comments for your decision..."></textarea>
                    </div>
                </div>
            `;
            
            document.getElementById('approvalContent').innerHTML = approvalContent;
            document.getElementById('approvalModal').setAttribute('data-control-number', controlNumber);
            this.openModal('approvalModal');
        } catch (error) {
            console.error('Error loading approval details:', error);
            this.showToast('Error loading request details', 'error');
        }
    }

    async processApproval(element) {
        const action = element.getAttribute('data-approval-action');
        const modal = document.getElementById('approvalModal');
        const controlNumber = modal.getAttribute('data-control-number');
        const commentsField = document.getElementById('approvalComments');
        const comments = commentsField ? commentsField.value : '';
        
        if (!controlNumber || !action) {
            this.showToast('Error: Missing required information', 'error');
            return;
        }

        if (!['approve', 'disapprove'].includes(action)) {
            this.showToast('Error: Invalid action', 'error');
            return;
        }

        try {
            const response = await fetch(`/leave/process/${controlNumber}/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCookie('csrftoken'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: action,
                    comments: comments
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showToast(data.message, 'success');
                this.closeModal('approvalModal');
                setTimeout(() => window.location.reload(), 1500);
            } else {
                this.showToast(data.message, 'error');
            }
        } catch (error) {
            console.error('Error processing approval:', error);
            this.showToast('Error processing request', 'error');
        }
    }

    // Data Refresh Functions
    async refreshMyRequests() {
        // Could implement AJAX refresh of requests table
        console.log('Refreshing my requests...');
    }

    async refreshApprovals() {
        // Could implement AJAX refresh of approvals table
        console.log('Refreshing approvals...');
    }

    async refreshData() {
        this.showToast('Refreshing data...', 'info');
        
        // Simple page reload for now, could be enhanced with AJAX
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }

    scrollToBalances() {
        const balancesSection = document.querySelector('.balance-cards-grid');
        if (balancesSection) {
            balancesSection.scrollIntoView({ 
                behavior: 'smooth',
                block: 'start'
            });
        }
    }

    // Calendar Functions
    initializeCalendar() {
        // Initialize leave data from Django context
        if (window.leaveCalendarData) {
            this.leaveData = window.leaveCalendarData.map(leave => ({
                startDate: new Date(leave.startDate),
                endDate: new Date(leave.endDate),
                leaveType: leave.leaveType,
                reason: leave.reason,
                code: leave.code,
                controlNumber: leave.controlNumber
            }));
        }
        
        // Bind calendar navigation
        this.bindCalendarEvents();
        this.updateCalendar();
    }

    bindCalendarEvents() {
        // Make sure changeMonth is available globally
        window.changeMonth = (delta) => {
            this.changeMonth(delta);
        };
        window.goToToday = () => {
            this.goToToday();
        };
    }

    goToToday() {
        this.currentDate = new Date();
        this.updateCalendar();
    }

    changeMonth(delta) {
        this.currentDate.setMonth(this.currentDate.getMonth() + delta);
        this.updateCalendar();
    }

    updateCalendar() {
        const monthNames = [
            "January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December"
        ];
        
        // Update month display
        const monthElement = document.getElementById('currentMonth');
        if (monthElement) {
            monthElement.textContent = `${monthNames[this.currentDate.getMonth()]} ${this.currentDate.getFullYear()}`;
        }
        
        // Generate calendar days
        const calendarDays = document.getElementById('calendar-days');
        if (!calendarDays) return;
        
        calendarDays.innerHTML = '';
        
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        
        // Get first day of month and number of days
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();
        
        // Get previous month's last days
        const prevMonth = new Date(year, month, 0);
        const daysInPrevMonth = prevMonth.getDate();
        
        // Add previous month's trailing days
        for (let i = startingDayOfWeek - 1; i >= 0; i--) {
            const dayNum = daysInPrevMonth - i;
            const dayDiv = this.createDayElement(dayNum, 'prev-month', new Date(year, month - 1, dayNum));
            calendarDays.appendChild(dayDiv);
        }
        
        // Add current month's days
        const today = new Date();
        for (let day = 1; day <= daysInMonth; day++) {
            const currentDay = new Date(year, month, day);
            const isToday = (year === today.getFullYear() && 
                            month === today.getMonth() && 
                            day === today.getDate());
            const dayDiv = this.createDayElement(day, isToday ? 'current-date' : '', currentDay);
            
            // Add leave events for this day
            this.addLeaveEventsToDay(dayDiv, currentDay);
            
            calendarDays.appendChild(dayDiv);
        }
        
        // Add next month's leading days to fill the grid
        const totalCells = calendarDays.children.length;
        const remainingCells = 42 - totalCells; // 6 rows × 7 days
        for (let day = 1; day <= remainingCells && day <= 14; day++) {
            const dayDiv = this.createDayElement(day, 'prev-month', new Date(year, month + 1, day));
            calendarDays.appendChild(dayDiv);
        }
    }

    createDayElement(dayNum, className, date) {
        const dayDiv = document.createElement('div');
        dayDiv.className = `calendar-day ${className}`;
        
        const dayNumber = document.createElement('span');
        dayNumber.className = 'day-number';
        dayNumber.textContent = dayNum;
        
        dayDiv.appendChild(dayNumber);
        return dayDiv;
    }

    addLeaveEventsToDay(dayDiv, date) {
        this.leaveData.forEach(leave => {
            if (this.isDateInRange(date, leave.startDate, leave.endDate)) {
                const event = document.createElement('div');
                event.className = `leave-event leave-${leave.code.toLowerCase()}`;
                
                // Create event content
                const eventTitle = document.createElement('div');
                eventTitle.className = 'leave-event-title';
                eventTitle.textContent = leave.leaveType;
                
                const eventNote = document.createElement('div');
                eventNote.className = 'leave-event-note';
                eventNote.textContent = `Note: ${leave.reason}`;
                
                event.appendChild(eventTitle);
                event.appendChild(eventNote);
                
                dayDiv.appendChild(event);
            }
        });
    }

    isDateInRange(date, startDate, endDate) {
        return date >= startDate && date <= endDate;
    }

    // Modal Management
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        this.currentModal = modal;
        modal.style.display = 'flex';
        modal.classList.add('show');
        document.body.classList.add('modal-open');
        
        // Focus management
        const firstFocusable = modal.querySelector('input, button, textarea, select');
        if (firstFocusable) {
            setTimeout(() => firstFocusable.focus(), 100);
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        // Start zoom-out animation by removing .show
        modal.classList.remove('show');
        document.body.classList.remove('modal-open');
        this.currentModal = null;

        // Wait for transition to finish before hiding
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300); // match transition duration in style.css

        // Reset modal content if needed
        if (modalId === 'applyLeaveModal') {
            this.resetApplyForm();
        }
        
        // Clear dynamic content
        const dynamicContent = modal.querySelector('[id$="Content"]');
        if (dynamicContent && modalId !== 'applyLeaveModal') {
            dynamicContent.innerHTML = '';
        }
    }

    closeCurrentModal() {
        if (this.currentModal) {
            this.closeModal(this.currentModal.id);
        }
    }

    // Utility Functions
    formatDate(date) {
        return date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric', 
            year: 'numeric' 
        });
    }

    getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    showToast(message, type = 'info') {
        const toastContainer = this.getOrCreateToastContainer();
        const toast = this.createToastElement(message, type);
        
        toastContainer.appendChild(toast);
        
        // Auto remove after 4 seconds
        setTimeout(() => {
            this.removeToast(toast);
        }, 4000);
    }

    getOrCreateToastContainer() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container';
            document.body.appendChild(container);
        }
        return container;
    }

    createToastElement(message, type) {
        const toast = document.createElement('div');
        toast.className = 'toast';
        
        const config = this.getToastConfig(type);
        
        toast.style.cssText = `
            background: ${config.bgColor};
            color: white;
            border: none;
            border-radius: var(--radius-md);
            padding: var(--space-md);
            margin-bottom: var(--space-sm);
            box-shadow: var(--shadow-lg);
            min-width: 300px;
            animation: toastSlideIn 0.3s ease-out;
            display: flex;
            align-items: center;
            gap: 12px;
        `;
        
        toast.innerHTML = `
            <i class="fas fa-${config.icon}" style="font-size: 1.1rem; opacity: 0.9;"></i>
            <span style="flex: 1;">${message}</span>
            <button onclick="this.parentElement.remove()" style="background: none; border: none; color: white; cursor: pointer; opacity: 0.7; font-size: 1.2rem; padding: 0; margin-left: 8px;">&times;</button>
        `;
        
        return toast;
    }

    getToastConfig(type) {
        const configs = {
            success: {
                icon: 'check-circle',
                bgColor: 'var(--success-color)'
            },
            error: {
                icon: 'exclamation-circle',
                bgColor: 'var(--error-color)'
            },
            warning: {
                icon: 'exclamation-triangle',
                bgColor: 'var(--warning-color)'
            },
            info: {
                icon: 'info-circle',
                bgColor: 'var(--primary-color)'
            }
        };
        
        return configs[type] || configs.info;
    }

    removeToast(toast) {
        if (!toast.parentNode) return;
        
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    // Animation helpers
    animateElement(element, animationClass = 'animate-fadeIn') {
        element.classList.add(animationClass);
        
        element.addEventListener('animationend', () => {
            element.classList.remove(animationClass);
        }, { once: true });
    }

    // Loading states
    showLoading(element, text = 'Loading...') {
        const originalContent = element.innerHTML;
        element.setAttribute('data-original-content', originalContent);
        element.disabled = true;
        element.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${text}`;
    }

    hideLoading(element) {
        const originalContent = element.getAttribute('data-original-content');
        if (originalContent) {
            element.innerHTML = originalContent;
            element.removeAttribute('data-original-content');
        }
        element.disabled = false;
    }

    // Error handling
    handleError(error, context = 'operation') {
        console.error(`Error in ${context}:`, error);
        
        let message = `An error occurred during ${context}`;
        
        if (error.message) {
            message = error.message;
        } else if (typeof error === 'string') {
            message = error;
        }
        
        this.showToast(message, 'error');
    }

    // Accessibility helpers
    announceToScreenReader(message) {
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        announcement.textContent = message;
        
        document.body.appendChild(announcement);
        
        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }

    // Performance optimization
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let lastFunc;
        let lastRan;
        return function() {
            const context = this;
            const args = arguments;
            if (!lastRan) {
                func.apply(context, args);
                lastRan = Date.now();
            } else {
                clearTimeout(lastFunc);
                lastFunc = setTimeout(function() {
                    if ((Date.now() - lastRan) >= limit) {
                        func.apply(context, args);
                        lastRan = Date.now();
                    }
                }, limit - (Date.now() - lastRan));
            }
        }
    }

    // Chart Management
    initializeCharts() {
        this.statusChart = null;
        this.leaveTypesChart = null;
        this.loadChartData();
        
        // Auto-refresh charts every minute
        setInterval(() => {
            this.loadChartData();
        }, 60000);
    }

    async loadChartData() {
        try {
            const response = await fetch('/leave/ajax/chart-data/');
            const data = await response.json();
            
            if (data.success) {
                this.updateFiscalYearIndicator(data.fiscal_year);
                this.createStatusChart(data.status_chart);
                this.createLeaveTypesChart(data.line_chart);
            } else {
                this.showChartError();
            }
        } catch (error) {
            console.error('Error loading chart data:', error);
            this.showChartError();
        }
    }

    updateFiscalYearIndicator(fiscalYear) {
        // Set fiscal year duration in both chart subtitle spans
        const fySpan1 = document.getElementById('fiscalYearDuration');
        const fySpan2 = document.getElementById('fiscalYearDuration2');
        if (fySpan1) {
            fySpan1.textContent = this.formatFiscalYearDuration(fiscalYear);
        }
        if (fySpan2) {
            fySpan2.textContent = this.formatFiscalYearDuration(fiscalYear);
        }
    }

    formatFiscalYearDuration(fiscalYear) {
        // fiscalYear is like '2025-2026'
        const [startYear, endYear] = fiscalYear.split('-');
        return `Fiscal Year: May ${startYear} - April ${endYear}`;
    }

    createStatusChart(data) {
        const ctx = document.getElementById('statusChart');
        if (!ctx) return;

        // Destroy existing chart
        if (this.statusChart) {
            this.statusChart.destroy();
        }

        const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
        const textColor = isDark ? '#f8fafc' : '#0f172a';

        this.statusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.data,
                    backgroundColor: data.backgroundColor,
                    borderWidth: 0,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: textColor,
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                family: 'Poppins',
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: isDark ? '#1e293b' : '#ffffff',
                        titleColor: textColor,
                        bodyColor: textColor,
                        borderColor: isDark ? '#334155' : '#e2e8f0',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.parsed}%`;
                            },
                            afterBody: function(context) {
                                return `Total Requests: ${data.total_requests}`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    createLeaveTypesChart(data) {
        const ctx = document.getElementById('leaveTypesChart');
        if (!ctx) return;

        // Destroy existing chart
        if (this.leaveTypesChart) {
            this.leaveTypesChart.destroy();
        }

        const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
        const textColor = isDark ? '#f8fafc' : '#0f172a';
        const gridColor = isDark ? '#334155' : '#e2e8f0';

        this.leaveTypesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: data.datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            color: textColor,
                            usePointStyle: true,
                            padding: 15,
                            font: {
                                family: 'Poppins',
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: isDark ? '#1e293b' : '#ffffff',
                        titleColor: textColor,
                        bodyColor: textColor,
                        borderColor: isDark ? '#334155' : '#e2e8f0',
                        borderWidth: 1,
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: textColor,
                            font: {
                                family: 'Poppins',
                                size: 11
                            }
                        },
                        grid: {
                            color: gridColor,
                            drawBorder: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: textColor,
                            font: {
                                family: 'Poppins',
                                size: 11
                            },
                            stepSize: 1,
                            callback: function(value) {
                                return Number.isInteger(value) ? value : '';
                            }
                        },
                        grid: {
                            color: gridColor,
                            drawBorder: false
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    showChartError() {
        const containers = ['statusChart', 'leaveTypesChart'];
        containers.forEach(containerId => {
            const container = document.getElementById(containerId);
            if (container) {
                const parent = container.parentElement;
                parent.innerHTML = `
                    <div class="chart-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>Failed to load chart data</span>
                    </div>
                `;
            }
        });
    }

    // Theme change handler for charts
    updateChartsForTheme() {
        if (this.statusChart) this.statusChart.destroy();
        if (this.leaveTypesChart) this.leaveTypesChart.destroy();
        
        setTimeout(() => {
            this.loadChartData();
        }, 100);
    }
}

// CSS for animations and toast (since we can't inline CSS)
const style = document.createElement('style');
style.textContent = `
    @keyframes toastSlideIn {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .animate-fadeIn {
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .field-valid input,
    .field-valid select,
    .field-valid textarea {
        border-color: var(--success-color);
    }

    .field-invalid input,
    .field-invalid select,
    .field-invalid textarea {
        border-color: var(--error-color);
    }

    .validation-message {
        margin-top: 4px;
        font-size: var(--font-size-xs);
    }

    .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
    }

    .toast-container {
        position: fixed;
        top: var(--space-lg);
        right: var(--space-lg);
        z-index: 9999;
        display: flex;
        flex-direction: column;
        gap: var(--space-sm);
    }

    @media (max-width: 768px) {
        .toast-container {
            left: var(--space-md);
            right: var(--space-md);
            top: var(--space-md);
        }
        
        .toast {
            min-width: auto !important;
        }
    }
`;
document.head.appendChild(style);

// Global functions for backward compatibility and external access
window.LeaveUser = {
    openApplyModal: () => window.leaveUserInterface?.openApplyLeaveModal(),
    viewDetails: (controlNumber) => {
        const element = document.createElement('div');
        element.setAttribute('data-control-number', controlNumber);
        window.leaveUserInterface?.viewLeaveDetails(element);
    },
    cancelLeave: (controlNumber) => {
        const element = document.createElement('div');
        element.setAttribute('data-control-number', controlNumber);
        window.leaveUserInterface?.cancelLeave(element);
    },
    openApproval: (controlNumber) => {
        const element = document.createElement('div');
        element.setAttribute('data-control-number', controlNumber);
        window.leaveUserInterface?.openApprovalModal(element);
    },
    processApproval: (action) => {
        const element = document.createElement('div');
        element.setAttribute('data-approval-action', action);
        window.leaveUserInterface?.processApproval(element);
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.leaveUserInterface = new LeaveUserInterface();
    console.log('Leave User Interface initialized');
    
    // Add theme change listener for charts
    const themeObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                if (window.leaveUserInterface) {
                    window.leaveUserInterface.updateChartsForTheme();
                }
            }
        });
    });
    
    themeObserver.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['data-theme']
    });
});