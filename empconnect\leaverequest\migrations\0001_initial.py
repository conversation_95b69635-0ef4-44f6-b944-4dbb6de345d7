# Generated by Django 5.0.3 on 2025-08-02 00:06

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LeaveType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=50, unique=True)),
                ('code', models.Char<PERSON>ield(max_length=10, unique=True)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='LeaveRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('control_number', models.CharField(editable=False, max_length=20, unique=True)),
                ('date_from', models.DateField()),
                ('date_to', models.DateField()),
                ('days_requested', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0.5)])),
                ('reason', models.TextField()),
                ('attachment', models.FileField(blank=True, null=True, upload_to='leave_attachments/')),
                ('status', models.CharField(choices=[('routing', 'Routing'), ('approved', 'Approved'), ('disapproved', 'Disapproved'), ('cancelled', 'Cancelled')], default='routing', max_length=20)),
                ('date_prepared', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('disapproved_at', models.DateTimeField(blank=True, null=True)),
                ('cancelled_at', models.DateTimeField(blank=True, null=True)),
                ('current_approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='pending_approvals', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_requests', to=settings.AUTH_USER_MODEL)),
                ('final_approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='final_approvals', to=settings.AUTH_USER_MODEL)),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='leaverequest.leavetype')),
            ],
            options={
                'ordering': ['-date_prepared'],
            },
        ),
        migrations.CreateModel(
            name='LeaveApprovalTimeline',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('submitted', 'Submitted'), ('approved', 'Approved'), ('disapproved', 'Disapproved'), ('cancelled', 'Cancelled'), ('forwarded', 'Forwarded')], max_length=20)),
                ('comments', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('actor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('leave_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='timeline', to='leaverequest.leaverequest')),
            ],
            options={
                'ordering': ['timestamp'],
            },
        ),
        migrations.CreateModel(
            name='LeaveApprovalFlow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sequence', models.PositiveIntegerField()),
                ('status', models.CharField(choices=[('routing', 'Routing'), ('approved', 'Approved'), ('disapproved', 'Disapproved'), ('cancelled', 'Cancelled')], default='routing', max_length=20)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('disapproved_at', models.DateTimeField(blank=True, null=True)),
                ('comments', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('approver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_approvals', to=settings.AUTH_USER_MODEL)),
                ('leave_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='approval_flow', to='leaverequest.leaverequest')),
            ],
            options={
                'ordering': ['sequence'],
                'unique_together': {('leave_request', 'approver', 'sequence')},
            },
        ),
        migrations.CreateModel(
            name='LeaveBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entitled', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0)])),
                ('used', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0)])),
                ('remaining', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0)])),
                ('valid_from', models.DateField()),
                ('valid_to', models.DateField()),
                ('validity_status', models.CharField(choices=[('active', 'Active'), ('expired', 'Expired'), ('for_conversion', 'For Conversion')], default='active', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_balances', to=settings.AUTH_USER_MODEL)),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='leaverequest.leavetype')),
            ],
            options={
                'ordering': ['-valid_from'],
                'unique_together': {('employee', 'leave_type', 'valid_from', 'valid_to')},
            },
        ),
    ]
