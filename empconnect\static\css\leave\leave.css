:root {
    --primary-color: #6366f1;
    --primary-hover: #5856eb;
    --primary-light: #a5b4fc;
    --secondary-color: #64748b;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --surface: #ffffff;
    --surface-hover: #f8fafc;
    
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #a3a3a3;
    --text-inverse: #ffffff;
    
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    
    --shadow-sm: 0 2px 8px 0 rgba(0, 0, 0, 0.066);
    --shadow-md: 0 4px 16px 0 rgba(0, 0, 0, 0.10);
    --shadow-lg: 0 8px 24px 0 rgba(0, 0, 0, 0.13);
    --shadow-xl: 0 16px 32px 0 rgba(0, 0, 0, 0.16);
    
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    --transition-speed: 0.7s;
    
    --sidebar-width-expanded: 250px;
    --sidebar-width-minimized: 70px;
    --header-height: 60px;
    --badge-bg: #ff3860;

    --leave-routing-color: #f59e0b;
    --leave-approved-color: #10b981;
    --leave-disapproved-color: #ef4444;
    --leave-cancelled-color: #6b7280;
    --balance-active-bg: rgba(16, 185, 129, 0.05);
    --balance-warning-bg: rgba(245, 158, 11, 0.05);
    --balance-expired-bg: rgba(107, 114, 128, 0.05);
}

[data-theme="dark"] {
    --primary-color: #818cf8;
    --primary-hover: #6366f1;
    --primary-light: #4338ca;
    --secondary-color: #94a3b8;
    --accent-color: #22d3ee;
    --success-color: #34d399;
    --warning-color: #fbbf24;
    --error-color: #f87171;
    
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --surface: #1e293b;
    --surface-hover: #334155;
    
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-inverse: #0f172a;
    
    --border-color: #334155;
    --border-hover: #475569;
    
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5), 0 4px 6px -4px rgb(0 0 0 / 0.5);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.6), 0 8px 10px -6px rgb(0 0 0 / 0.6);
    
    --balance-active-bg: rgba(52, 211, 153, 0.1);
    --balance-warning-bg: rgba(251, 191, 36, 0.1);
    --balance-expired-bg: rgba(156, 163, 175, 0.1);
}

.leave-section {
    display: flex;
    flex-direction: column;
    gap: 0;
}

.reminder-warning {
  background: #fffbe6;
  color: #f7b500;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
}
.reminder-warning-icon {
  background: #fffbe6;
  color: #f7b500;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  margin-right: 16px;
}
.reminder-warning-title {
  color: #906900;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 4px;
}
.reminder-warning-meta {
  color: #906900;
  font-size: 15px;
}
@media (prefers-color-scheme: dark) {
  .reminder-warning {
    background: #2d2a1e;
    color: #f7b500;
  }
  .reminder-warning-icon {
    background: #2d2a1e;
    color: #f7b500;
  }
  .reminder-warning-title,
  .reminder-warning-meta {
    color: #f7b500;
  }
}
.leave-balance-icon-circle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42px;
    height: 42px;
    border-radius: 50%;
    box-shadow: 0 1px 4px rgba(0,0,0,0.07);
    background: #f5f5f5;
    opacity: 0.7;
    font-size: 1.6rem;
}

.leave-balance-icon-circle.blue { background: #e6f0ff; color: #2563eb; }
.leave-balance-icon-circle.orange { background: #fff7e6; color: #f59e0b; }
.leave-balance-icon-circle.green { background: #e6f9e6; color: #22c55e; }
.leave-balance-icon-circle.red { background: #ffeaea; color: #ef4444; }

.leave-balance-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.08rem;
  font-weight: 600;
  margin-bottom: 6px;
}
.leave-balance-id {
  color: #64748b;
  font-weight: 500;
  margin-right: 6px;
}

.leave-balance-title {
    font-size: var(--font-size-md);
    color: var(--text-primary);
    font-weight: 500;
}

[data-theme="dark"] .leave-balance-title,
[data-theme="dark"] .date-range-text {
  color: white !important;
}

.leave-balance-status {
  display: inline-block;
  background: #e6f0fa;
  color: #2563eb;
  font-size: 0.95rem;
  font-weight: 600;
  border-radius: 12px;
  padding: 2px 10px;
  margin-left: auto;
}
.leave-balance-status.in-progress {
  background: #e6f0fa;
  color: #2563eb;
}

.leave-balance-main-row {
  display: flex;
  flex-direction: column;
  /* gap: 4px; */
}

.leave-balance-info-row {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  color: var(--text-muted);
}
.leave-balance-company,
.leave-balance-country,
.leave-balance-user,
.leave-balance-category {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}
/* Leave Balance Card Style */
.leave-balance-card {
  display: flex;
  align-items: center;
  /* border-bottom: 0.5px solid var(--border-color); */
  gap: 18px;
  padding: var(--space-sm) 0;
  margin-top: var(--space-md);
}
.leave-balance-details {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}
.leave-balance-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}
.leave-balance-sub {
  font-size: 0.95rem;
  color: #64748b;
  font-weight: 400;
  margin-left: 8px;
}
.leave-balance-remaining {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: var(--font-size-xs);
    padding: var(--space-sm) 0;
}
.leave-balance-change {
  display: flex;
  align-items: center;
  gap: 10px;
}
.leave-balance-change-value {
  font-size: 1rem;
  font-weight: 600;
  color: #334155;
}
.leave-balance-change-badge {
  display: inline-flex;
  align-items: center;
  font-size: 0.95rem;
  font-weight: 700;
  border-radius: 16px;
  padding: 2px 12px;
  margin-left: 2px;
}
.leave-balance-change-badge.up {
  background: #e6f9f0;
  color: #16a34a;
}
.leave-balance-change-badge.down {
  background: #fbeaea;
  color: #ef4444;
}
/* Balance Set Card Styles */
.balance-set-card {
    background: white;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
    padding: var(--space-md);
}

.balance-period-info {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.balance-period-text {
    font-size: var(--font-size-sm);
    font-weight: 400;
    color: var(--text-muted);
}

.balance-set-card-body {
    padding: 0 var(--space-md);
}

.balance-list-item{
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md);
}
.balance-set-table-container {
    margin: 0;
}

.balance-set-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    border: none;
    font-size: var(--font-size-sm);
    overflow: hidden;
}
.balance-set-table th {
    background: var(--surface-hover);
    color: var(--text-primary);
    font-weight: 700;
    font-size: var(--font-size-sm);
    border-bottom: 2px solid var(--border-color);
    padding: var(--space-md) var(--space-sm);
    letter-spacing: 0.5px;
}
.balance-set-table td {
    background: #fff;
    color: var(--text-primary);
    padding: var(--space-md) var(--space-sm);
    border-bottom: 1px solid var(--border-light);
}
.balance-set-row:last-child td {
    border-bottom: none;
}
.balance-status-badge {
    display: inline-block;
    padding: 4px 14px;
    border-radius: 16px;
    font-size: var(--font-size-xs);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-right: var(--space-xs);
    border: none;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}
.balance-status-badge.status-active {
    background: var(--success-color);
    color: var(--text-inverse);
}
.balance-status-badge.status-for_conversion {
    background: var(--warning-color);
    color: var(--text-inverse);
}
.balance-status-badge.status-expired {
    background: var(--text-muted);
    color: var(--text-inverse);
}
.balance-progress {
    width: 100px;
    min-width: 60px;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-top: 2px;
}
.progress-bar {
    width: 100%;
    height: 100%;
    position: relative;
}
.progress-fill {
    height: 100%;
    min-width: 8px;
    background: linear-gradient(90deg, var(--success-color), var(--warning-color));
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
    position: relative;
}
/* Enhanced Leave Balance Table Design */
.balance-set-table-container {
    margin-top: var(--space-md);
    margin-bottom: var(--space-md);
    overflow-x: auto;
}

.balance-set-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    font-size: var(--font-size-sm);
}

.balance-set-table th, .balance-set-table td {
    padding: var(--space-md) var(--space-sm);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.balance-set-table th {
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 600;
}

.balance-set-row:last-child td {
    border-bottom: none;
}

.balance-set-row .balance-icon {
    margin-right: var(--space-xs);
    font-size: var(--font-size-lg);
    vertical-align: middle;
}

.balance-type-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-left: var(--space-xs);
}

.balance-status-badge {
    display: inline-block;
    padding: 2px 10px;
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-right: var(--space-xs);
}
.balance-status-badge.status-active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--leave-approved-color);
    border: 1px solid rgba(16, 185, 129, 0.3);
}
.balance-status-badge.status-for_conversion {
    background: rgba(245, 158, 11, 0.1);
    color: var(--leave-routing-color);
    border: 1px solid rgba(245, 158, 11, 0.3);
}
.balance-status-badge.status-expired {
    background: rgba(107, 114, 128, 0.1);
    color: var(--leave-cancelled-color);
    border: 1px solid rgba(107, 114, 128, 0.3);
}
.balance-progress {
    width: 100px;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-top: 2px;
}
.progress-bar {
    width: 100%;
    height: 100%;
    position: relative;
}
.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--warning-color));
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
    position: relative;
}

/* Section Headers */
.section-title {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-lg);
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    font-weight: 600;
}

.section-title i {
    color: var(--primary-color);
}

/* Leave Balance Cards */
.balance-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-2xl);
}

.balance-card {
    background: var(--surface);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.balance-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--border-color);
    transition: all var(--transition-normal);
}

.balance-card.balance-active {
    border-color: var(--success-color);
    background: var(--balance-active-bg);
}

.balance-card.balance-active::before {
    background: var(--success-color);
}

.balance-card.balance-expired {
    border-color: var(--warning-color);
    background: var(--balance-warning-bg);
}

.balance-card.balance-expired::before {
    background: var(--warning-color);
}

.balance-card.balance-inactive {
    opacity: 0.6;
    border-color: var(--text-muted);
    background: var(--balance-expired-bg);
}

.balance-card.balance-inactive::before {
    background: var(--text-muted);
}

.balance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-md);
}

.balance-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.balance-status {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.balance-status.active {
    background: var(--success-color);
    color: var(--text-inverse);
}

.balance-status.for_conversion {
    background: var(--warning-color);
    color: var(--text-inverse);
}

.balance-status.expired {
    background: var(--text-muted);
    color: var(--text-inverse);
}

.balance-period {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-lg);
    padding: var(--space-sm);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
}

.balance-period i {
    color: var(--primary-color);
}

.balance-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-lg);
    margin-bottom: var(--space-lg);
}

.balance-stat {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-bottom: var(--space-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.stat-value {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.stat-value.used {
    color: var(--leave-routing-color);
}

.stat-value.remaining {
    color: var(--leave-approved-color);
}

.balance-progress {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    width: 100%;
    height: 100%;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--warning-color));
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}


@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Overview Grid */
.overview-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-xl);
    margin-bottom: var(--space-xl);
}

/* Recent Leaves */
.recent-leaves-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.recent-leave-item {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: var(--space-lg);
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.recent-leave-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: transform var(--transition-normal);
}

.recent-leave-item:hover {
    background: var(--surface-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.recent-leave-item:hover::before {
    transform: scaleY(1);
}

.leave-info {
    flex: 1;
    min-width: 0;
}

.leave-control {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-xs);
}

.leave-type {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: var(--space-xs);
}

.leave-duration {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.leave-status {
    display: flex;
    align-items: center;
}

.leave-actions {
    display: flex;
    gap: var(--space-xs);
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all var(--transition-fast);
}

.status-routing {
    background: rgba(245, 158, 11, 0.1);
    color: var(--leave-routing-color);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-approved {
    background: rgba(16, 185, 129, 0.1);
    color: var(--leave-approved-color);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-disapproved {
    background: rgba(239, 68, 68, 0.1);
    color: var(--leave-disapproved-color);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-cancelled {
    background: rgba(107, 114, 128, 0.1);
    color: var(--leave-cancelled-color);
    border: 1px solid rgba(107, 114, 128, 0.3);
}

/* Quick Actions */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-xl);
    background: var(--surface);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-normal);
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.quick-action-btn:hover::before {
    left: 100%;
}

.quick-action-btn i {
    font-size: var(--font-size-2xl);
    transition: transform var(--transition-normal);
}

.quick-action-btn:hover i {
    transform: scale(1.1);
}

/* Data Tables */
.table-container {
    background: var(--surface);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
}

.data-table th {
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 600;
    padding: var(--space-lg) var(--space-md);
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
    position: relative;
    cursor: pointer;
}

.data-table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.data-table th:hover::after {
    transform: scaleX(1);
}

.data-table td {
    padding: var(--space-md);
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.data-table tr {
    transition: all var(--transition-fast);
}

.data-table tr:hover {
    background: var(--surface-hover);
}

.data-table tr:last-child td {
    border-bottom: none;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: var(--space-xs);
    justify-content: flex-end;
}

.btn-sm {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--font-size-xs);
    min-width: 32px;
    height: 32px;
}

.btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    padding: 0;
}

/* Table Actions */
.table-actions {
    padding: var(--space-lg);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
}

/* Admin Stats */
.admin-overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-2xl);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: var(--space-xl);
    background: var(--surface);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: transform var(--transition-normal);
}

.stat-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-card:hover::before {
    transform: scaleY(1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--text-inverse);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    box-shadow: var(--shadow-md);
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-content h4 {
    margin: 0 0 var(--space-xs) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--text-primary);
    line-height: 1;
}

/* Admin Actions */
.admin-actions {
    display: flex;
    gap: var(--space-lg);
    justify-content: center;
    margin-bottom: var(--space-xl);
}

/* Employee Info */
.employee-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.employee-name {
    font-weight: 600;
    color: var(--text-primary);
}

.employee-id {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
    font-family: 'Courier New', monospace;
}

/* Control Number Styling */
.control-number {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    border: 1px solid rgba(99, 102, 241, 0.2);
}

/* Form Styling */
.form-row {
    display: grid;
    gap: var(--space-lg);
    margin-bottom: var(--space-lg);
}

.form-row-2col {
    grid-template-columns: 1fr 1fr;
}

.form-row-3col {
    grid-template-columns: repeat(3, 1fr);
}

.form-row-4col {
    grid-template-columns: repeat(4, 1fr);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.form-control {
    padding: var(--space-sm) var(--space-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--surface);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-text {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-top: var(--space-xs);
}

/* Balance and Days Display */
.days-display {
    padding: var(--space-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    opacity: 1;
    max-height: 500px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
    gap: 12px;
}

.days-display.days-hidden {
    opacity: 0;
    max-height: 0;
    padding: 0 var(--space-md);
    margin: 0;
    transform: translateY(-10px);
}

.days-display.days-show {
    opacity: 1;
    max-height: 500px;
    padding: var(--space-md);
    transform: translateY(0);
}

.days-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
    width: 100%;
    position: relative;
}

.days-count {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-md);
}

.days-range {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

.days-text {
    color: var(--text-secondary);
    font-style: italic;
}

/* Notification Badge */
.notification-badge {
    background: var(--error-color);
    color: var(--text-inverse);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    margin-left: var(--space-xs);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.approval-count {
    background: var(--primary-color);
    color: var(--text-inverse);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: var(--space-2xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--space-lg);
    color: var(--text-muted);
    opacity: 0.5;
}

.empty-state h4,
.empty-state h5 {
    margin: 0 0 var(--space-sm) 0;
    color: var(--text-secondary);
}

.empty-state p {
    margin: 0 0 var(--space-lg) 0;
    color: var(--text-muted);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--space-lg);
    right: var(--space-lg);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.toast {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--space-md);
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    animation: toastIn 0.3s ease-out;
    transition: all var(--transition-normal);
}

@keyframes toastIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Admin-specific Components */
.bulk-actions {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--surface);
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-lg);
    padding: var(--space-md) var(--space-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    animation: slideUp 0.3s ease-out;
}

.bulk-actions-content {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.bulk-action-buttons {
    display: flex;
    gap: var(--space-sm);
}

.selected-count {
    font-weight: 600;
    color: var(--text-primary);
}

/* Export Options */
.export-options {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

.export-option {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.export-option:hover {
    border-color: var(--primary-color);
    background: var(--surface-hover);
}

.export-option input[type="radio"]:checked + label {
    color: var(--primary-color);
    font-weight: 600;
}

.export-option input[type="radio"] {
    margin: 0;
}

.export-option label {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    cursor: pointer;
    flex: 1;
    margin: 0;
}

/* Search and Filter */
.card-header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.results-count {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: center;
    padding: var(--space-lg);
    border-top: 1px solid var(--border-color);
}

.pagination {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--surface);
    color: var(--text-primary);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.pagination-btn:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
}

.pagination-info {
    padding: 0 var(--space-md);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Sort Indicators */
.sort-asc::after {
    content: ' ↑';
    color: var(--primary-color);
}

.sort-desc::after {
    content: ' ↓';
    color: var(--primary-color);
}

/* Form Validation */
.field-valid input,
.field-valid select,
.field-valid textarea {
    border-color: var(--success-color);
}

.field-invalid input,
.field-invalid select,
.field-invalid textarea {
    border-color: var(--error-color);
}

.validation-message {
    margin-top: 4px;
    font-size: var(--font-size-xs);
}

.text-error {
    color: var(--error-color);
}

.text-muted {
    color: var(--text-muted);
}

.text-center {
    text-align: center;
}

/* Animation Classes */
.stat-update {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .overview-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }
    
    .balance-cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .admin-overview-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .form-row-4col {
        grid-template-columns: repeat(2, 1fr);
    }
    .chart-container {
        height: 300px;
        min-height: 200px;
        max-height: 60vh;
        display: flex;
        align-items: stretch;
    }
    #leaveTypesChart {
        width: 100% !important;
        height: 100% !important;
        display: block;
    }
}

@media (max-width: 768px) {
    .leave-section {
        height: 100vh;
    }
    .page-actions {
        justify-content: center;
    }
    
    .balance-cards-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row-2col,
    .form-row-3col,
    .form-row-4col {
        grid-template-columns: 1fr;
    }
    
    .balance-stats {
        grid-template-columns: 1fr;
        gap: var(--space-sm);
    }
    
    .recent-leave-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-md);
    }
    
    .action-buttons {
        width: 100%;
        justify-content: flex-start;
    }
    
    .admin-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .tab-list {
        justify-content: flex-start;
    }
    
    .tab {
        min-width: 120px;
        text-align: center;
    }
    
    .bulk-actions {
        left: var(--space-md);
        right: var(--space-md);
        transform: none;
    }
    
    .bulk-actions-content {
        flex-direction: column;
        gap: var(--space-md);
    }
    
    .bulk-action-buttons {
        width: 100%;
        justify-content: center;
    }
    
    .toast-container {
        left: var(--space-md);
        right: var(--space-md);
        top: var(--space-md);
    }
    
    .toast {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .admin-overview-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: var(--space-lg);
        gap: var(--space-md);
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    .stat-number {
        font-size: var(--font-size-2xl);
    }
    
    .quick-action-btn {
        padding: var(--space-lg);
    }
    
    .quick-action-btn i {
        font-size: var(--font-size-xl);
    }
    
    .balance-card {
        padding: var(--space-md);
    }
    
    .recent-leave-item {
        padding: var(--space-md);
    }
}

/* Print Styles */
@media print {
    .page-actions,
    .action-buttons,
    .admin-actions,
    .tab-list,
    .toast-container,
    .bulk-actions {
        display: none !important;
    }
    
    .balance-card,
    .stat-card,
    .recent-leave-item {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .balance-card,
    .stat-card,
    .recent-leave-item {
        border-width: 3px;
    }
    
    .status-badge {
        border-width: 2px;
    }
    
    .form-control:focus {
        outline: 3px solid var(--primary-color);
        outline-offset: 2px;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Section Headers */
.section-title {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-lg);
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    font-weight: 600;
}

.section-title i {
    color: var(--primary-color);
}

/* Leave Balance Cards */
.balance-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-2xl);
}

.balance-card {
    background: var(--surface);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.balance-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--border-color);
    transition: all var(--transition-normal);
}

.balance-card.balance-active {
    border-color: var(--success-color);
    background: var(--balance-active-bg);
}

.balance-card.balance-active::before {
    background: var(--success-color);
}

.balance-card.balance-expired {
    border-color: var(--warning-color);
    background: var(--balance-warning-bg);
}

.balance-card.balance-expired::before {
    background: var(--warning-color);
}

.balance-card.balance-inactive {
    opacity: 0.6;
    border-color: var(--text-muted);
    background: var(--balance-expired-bg);
}

.balance-card.balance-inactive::before {
    background: var(--text-muted);
}

.balance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-md);
}

.balance-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.balance-status {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.balance-status.active {
    background: var(--success-color);
    color: var(--text-inverse);
}

.balance-status.for_conversion {
    background: var(--warning-color);
    color: var(--text-inverse);
}

.balance-status.expired {
    background: var(--text-muted);
    color: var(--text-inverse);
}

.balance-period {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-lg);
    padding: var(--space-sm);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
}

.balance-period i {
    color: var(--primary-color);
}

.balance-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-lg);
    margin-bottom: var(--space-lg);
}

.balance-stat {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-bottom: var(--space-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.stat-value {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.stat-value.used {
    color: var(--leave-routing-color);
}

.stat-value.remaining {
    color: var(--leave-approved-color);
}

.balance-progress {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--warning-color));
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Overview Grid */
.overview-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-xl);
    margin-bottom: var(--space-xl);
}

/* Recent Leaves */
.recent-leaves-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.recent-leave-item {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: var(--space-lg);
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.recent-leave-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: transform var(--transition-normal);
}

.recent-leave-item:hover {
    background: var(--surface-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.recent-leave-item:hover::before {
    transform: scaleY(1);
}

.leave-info {
    flex: 1;
    min-width: 0;
}

.leave-control {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-xs);
}

.leave-type {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: var(--space-xs);
}

.leave-duration {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.leave-status {
    display: flex;
    align-items: center;
}

.leave-actions {
    display: flex;
    gap: var(--space-xs);
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all var(--transition-fast);
}

.status-routing {
    background: rgba(245, 158, 11, 0.1);
    color: var(--leave-routing-color);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-approved {
    background: rgba(16, 185, 129, 0.1);
    color: var(--leave-approved-color);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-disapproved {
    background: rgba(239, 68, 68, 0.1);
    color: var(--leave-disapproved-color);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-cancelled {
    background: rgba(107, 114, 128, 0.1);
    color: var(--leave-cancelled-color);
    border: 1px solid rgba(107, 114, 128, 0.3);
}

/* Quick Actions */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-xl);
    background: var(--surface);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-normal);
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.quick-action-btn:hover::before {
    left: 100%;
}

.quick-action-btn i {
    font-size: var(--font-size-2xl);
    transition: transform var(--transition-normal);
}

.quick-action-btn:hover i {
    transform: scale(1.1);
}

/* Data Tables */
.table-container {
    background: var(--surface);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
}

.data-table th {
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 600;
    padding: var(--space-lg) var(--space-md);
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
    position: relative;
}

.data-table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.data-table th:hover::after {
    transform: scaleX(1);
}

.data-table td {
    padding: var(--space-md);
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.data-table tr {
    transition: all var(--transition-fast);
}

.data-table tr:hover {
    background: var(--surface-hover);
}

.data-table tr:last-child td {
    border-bottom: none;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: var(--space-xs);
    justify-content: flex-end;
}

.btn-sm {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--font-size-xs);
    min-width: 32px;
    height: 32px;
}

.btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    padding: 0;
}

/* Table Actions */
.table-actions {
    padding: var(--space-lg);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
}

/* Admin Stats */
.admin-overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-2xl);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: var(--space-xl);
    background: var(--surface);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: transform var(--transition-normal);
}

.stat-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-card:hover::before {
    transform: scaleY(1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--text-inverse);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    box-shadow: var(--shadow-md);
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-content h4 {
    margin: 0 0 var(--space-xs) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--text-primary);
    line-height: 1;
}

/* Admin Actions */
.admin-actions {
    display: flex;
    gap: var(--space-lg);
    justify-content: center;
    margin-bottom: var(--space-xl);
}

/* Employee Info */
.employee-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.employee-name {
    font-weight: 600;
    color: var(--text-primary);
}

.employee-id {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
    font-family: 'Courier New', monospace;
}

/* Control Number Styling */
.control-number {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    border: 1px solid rgba(99, 102, 241, 0.2);
}

/* Form Styling */
.form-row {
    display: grid;
    gap: var(--space-lg);
    margin-bottom: var(--space-lg);
}

.form-row-2col {
    grid-template-columns: 1fr 1fr;
}

/* Notification Badge */
.notification-badge {
    background: var(--error-color);
    color: var(--text-inverse);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    margin-left: var(--space-xs);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.approval-count {
    background: var(--primary-color);
    color: var(--text-inverse);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: var(--space-2xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--space-lg);
    color: var(--text-muted);
    opacity: 0.5;
}

.empty-state h4,
.empty-state h5 {
    margin: 0 0 var(--space-sm) 0;
    color: var(--text-secondary);
}

.empty-state p {
    margin: 0 0 var(--space-lg) 0;
    color: var(--text-muted);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--space-lg);
    right: var(--space-lg);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.toast {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--space-md);
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    animation: toastIn 0.3s ease-out;
    transition: all var(--transition-normal);
}

@keyframes toastIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .overview-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }
    
    .balance-cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .admin-overview-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .page-actions {
        justify-content: center;
    }
    
    .balance-cards-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row-2col {
        grid-template-columns: 1fr;
    }
    
    .balance-stats {
        grid-template-columns: 1fr;
        gap: var(--space-sm);
    }
    
    .recent-leave-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-md);
    }
    
    .action-buttons {
        width: 100%;
        justify-content: flex-start;
    }
    
    .admin-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .tab-list {
        justify-content: flex-start;
    }
    
    .tab {
        min-width: 120px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .admin-overview-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: var(--space-lg);
        gap: var(--space-md);
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    .stat-number {
        font-size: var(--font-size-2xl);
    }
    
    .quick-action-btn {
        padding: var(--space-lg);
    }
    
    .quick-action-btn i {
        font-size: var(--font-size-xl);
    }
    
    .balance-card {
        padding: var(--space-md);
    }
    
    .recent-leave-item {
        padding: var(--space-md);
    }
    
    .toast-container {
        left: var(--space-md);
        right: var(--space-md);
        top: var(--space-md);
    }
    
    .toast {
        min-width: auto;
    }
}

/* Print Styles */
@media print {
    .page-actions,
    .action-buttons,
    .admin-actions,
    .tab-list,
    .toast-container {
        display: none !important;
    }
    
    .balance-card,
    .stat-card,
    .recent-leave-item {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
}

/* Animation Classes */
.animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slideUp {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .balance-card,
    .stat-card,
    .recent-leave-item {
        border-width: 3px;
    }
    
    .status-badge {
        border-width: 2px;
    }
    
    .form-control:focus {
        outline: 3px solid var(--primary-color);
        outline-offset: 2px;
    }
}

/* Overview Two Column Layout */
.overview-two-column {
    display: flex;
    gap: var(--space-lg);
    /* height: calc(100vh - 280px); */
    margin-bottom: var(--space-md);
}

.overview-left-column {
    flex: 0 0 400px;
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.overview-right-column {
    flex: 1;
    min-width: 0;
}

/* Leave Balance Summary */
.leave-balance-summary {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
    border-radius: 12px;
}

.balance-summary-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: var(--space-sm);
}

.balance-summary-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border-left: 4px solid transparent;
}

.balance-summary-card.balance-sl {
    background: #e8f4f8;
    border-left-color: #3498db;
}

.balance-summary-card.balance-vl {
    background: #f0e8ff;
    border-left-color: #9b59b6;
}

.balance-summary-card.balance-el {
    background: #fff5e6;
    border-left-color: #f39c12;
}

.balance-summary-card.balance-default {
    background: #e8f5e8;
    border-left-color: #2ecc71;
}

.balance-summary-card.balance-holiday {
    background: #fff8e1;
    border-left-color: #ffc107;
}

.balance-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.balance-sl .balance-icon {
    background: #3498db;
    color: white;
}

.balance-vl .balance-icon {
    background: #9b59b6;
    color: white;
}

.balance-el .balance-icon {
    background: #f39c12;
    color: white;
}

.balance-default .balance-icon {
    background: #2ecc71;
    color: white;
}

.balance-holiday .balance-icon {
    background: #ffc107;
    color: white;
}

.balance-info {
    flex: 1;
}

.balance-count {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.balance-status-text {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.see-holiday-link {
    color: #3498db;
    text-decoration: none;
}

.see-holiday-link:hover {
    text-decoration: underline;
}

.balance-note {
    font-size: 0.85rem;
    color: #7f8c8d;
    line-height: 1.4;
    margin: 1.5rem 0;
}

.btn-full-width {
    width: 100%;
    margin-top: 1rem;
}

/* Recent Activities */
.recent-activities {
    background: white;
    border-radius: 12px;
    padding: var(--space-md);
    border: 1px solid #e9ecef;
    flex: 1;
    overflow-y: auto;
}

.activities-title {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    border-radius: 8px;
    background: #fffbe6; 
    color: #f7b50092; 
    padding: 12px 16px;
}

.reminder-icon {
    width: 40px; 
    height: 40px; 
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    flex-shrink: 0;
    background: #fffbe6; 
    color: #f7b500;
}

.activity-icon.status-approved {
    background: #d4edda;
    color: #155724;
}

.activity-icon.status-disapproved {
    background: #f8d7da;
    color: #721c24;
}

.activity-icon.status-routing {
    background: #fff3cd;
    color: #856404;
}

.activity-icon:not([class*="status-"]) {
    background: #e9ecef;
    color: #6c757d;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.activity-meta {
    font-size: 0.85rem;
    color: #7f8c8d;
}

/* Calendar Styles */
.leave-calendar-container {
    background: white;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e9ecef;
    flex-shrink: 0;
    height: 80px;
    box-sizing: border-box;
}

[data-theme="dark"] .calendar-header {
    border-bottom: 0.5px solid var(--border-color);
}

.calendar-view-controls {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s;
}

.view-btn:hover {
    background: #f8f9fa;
}

.view-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.calendar-date-range {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.nav-btn:hover {
    background: #f8f9fa;
}

.date-range-text {
    font-weight: 500;
    color: #2c3e50;
    min-width: 180px;
    text-align: center;
}

.calendar-grid {
    flex: 1;
    padding: 1rem 2rem 2rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    margin-bottom: 1rem;
    flex-shrink: 0;
}

.weekday {
    padding: 0.75rem;
    text-align: center;
    font-weight: 500;
    color: #6c757d;
    font-size: 0.9rem;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    border-radius: var(--radius-md);
    border: 0.2px solid var(--border-color);
    overflow: hidden;
    flex: 1;
}

.calendar-day {
    background: var(--surface);
    padding: 0.5rem;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

[data-theme="dark"] .calendar-day {
    color: #fff;
}

.calendar-day.prev-month {
    background: #f8f9fa;
    color: #adb5bd;
}
[data-theme="dark"] .calendar-day.prev-month {
    background: #23272f;
    color: #6c757d;
}

.calendar-day.current-date {
    background: var(--primary-light);
    color: white;
    border-radius: var(--radius-md);
}

.calendar-day.current-date .day-number {
    color: white;
}

.day-number {
    font-weight: 500;
    font-size: 0.9rem;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}
[data-theme="dark"] .day-number {
    color: #fff;
}

.leave-event {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
    text-align: left;
    color: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12);
}

.leave-event-title {
    font-weight: bold;
    font-size: 0.7rem;
    margin-bottom: 2px;
}

.leave-event-note {
    font-size: 0.65rem;
    opacity: 0.9;
    line-height: 1.2;
}

/* Leave type specific colors */
.leave-event.leave-sl {
    background: #f87171; /* Red for Sick Leave */
    border-left: 3px solid #dc2626;
}

.leave-event.leave-vl {
    background: #60a5fa; /* Blue for Vacation Leave */
    border-left: 3px solid #2563eb;
}

.leave-event.leave-el {
    background: #34d399; /* Green for Emergency Leave */
    border-left: 3px solid #059669;
}

.leave-event.leave-ml {
    background: #a78bfa; /* Purple for Maternity Leave */
    border-left: 3px solid #7c3aed;
}

.leave-event.leave-pl {
    background: #fbbf24; /* Yellow for Paternity Leave */
    border-left: 3px solid #f59e0b;
    color: #1f2937;
}

.leave-event.leave-bl {
    background: #fb7185; /* Pink for Bereavement Leave */
    border-left: 3px solid #e11d48;
}

/* Default for other leave types */
.leave-event:not([class*="leave-"]) {
    background: #9ca3af;
    border-left: 3px solid #6b7280;
}

/* Legacy styles for backward compatibility */
.leave-event.leave-sick {
    background: #f87171;
    border-left: 3px solid #dc2626;
}

.leave-event.leave-paid {
    background: #60a5fa;
    border-left: 3px solid #2563eb;
}

.leave-event.leave-paid-alt {
    background: #34d399;
    border-left: 3px solid #059669;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .overview-two-column {
        flex-direction: column;
        height: auto;
    }
    
    .overview-left-column {
        flex: none;
        width: 100%;
    }
    
    .overview-right-column {
        flex: none;
    }
}

@media (max-width: 768px) {
    .calendar-header {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .calendar-grid {
        padding: 1rem;
    }
    
    .calendar-day {
        min-height: 60px;
        padding: 0.25rem;
    }
    
    .leave-event {
        font-size: 0.7rem;
        padding: 0.125rem 0.25rem;
    }
}

/* Balance Set Totals Section */
.balance-set-totals {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-top: 2px solid #e5e7eb;
    margin-top: 8px;
    border-radius: 0 0 12px 12px;
    gap: 16px;
}

.balance-set-total-item {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    justify-content: center;
    padding: 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 0;
}

.balance-set-total-item:hover {
    background: rgba(255, 255, 255, 0.7);
    transform: translateY(-2px);
}

.balance-total-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #3b82f6;
    color: white;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    flex-shrink: 0;
}

.balance-total-icon i {
    font-size: 16px;
}

.balance-set-total-item:nth-child(1) .balance-total-icon {
    background: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.balance-set-total-item:nth-child(2) .balance-total-icon {
    background: #22c55e;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.balance-set-total-item:nth-child(3) .balance-total-icon {
    background: #f59e0b;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.balance-total-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 0;
}

.balance-total-label {
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    margin-bottom: 2px;
    flex-shrink: 0;
}

.balance-total-value {
    font-size: 18px;
    font-weight: 700;
    color: #1e293b;
    flex-shrink: 0;
}

/* Responsive adjustments for balance totals */
@media (max-width: 768px) {
    .balance-set-totals {
        flex-direction: column;
        gap: 12px;
        padding: 16px;
    }
    
    .balance-set-total-item {
        width: 100%;
        justify-content: flex-start;
        padding: 16px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 8px;
    }
    
    .balance-total-icon {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }
    
    .balance-total-label {
        font-size: 12px;
    }
    
    .balance-total-value {
        font-size: 16px;
    }
}

/* Chart Analytics Styles */
.chart-analytics-container {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--space-lg);
}

.chart-analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
    flex-wrap: wrap;
    gap: var(--space-sm);
}

.chart-analytics-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.fiscal-year-indicator {
    background: var(--primary-color);
    color: var(--text-inverse);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.chart-grid {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-lg);
    margin-top: var(--space-lg);
}

.chart-card {
    width: 100%;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    border: 0.5px solid var(--border-color);
    transition: all var(--transition-normal);
}

[data-theme="dark"] .chart-card,
[data-theme="dark"] .balance-set-card,
[data-theme="dark"] .recent-activities,
[data-theme="dark"] .leave-calendar-container {
  border: 1.5px solid var(--border-color) !important;
  background: var(--bg-secondary) !important;
}

.chart-card-header {
    margin-bottom: var(--space-lg);
}

.chart-card-header h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--space-xs) 0;
}

.chart-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.chart-container canvas {
    width: 100% !important;
    height: 100% !important;
}

/* Chart Loading State */
.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.chart-loading::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: var(--space-sm);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Chart Error State */
.chart-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: var(--error-color);
    font-size: var(--font-size-sm);
    flex-direction: column;
    gap: var(--space-sm);
}

.chart-error i {
    font-size: var(--font-size-xl);
    opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .chart-grid {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }
}

@media (max-width: 768px) {
    .chart-analytics-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-sm);
    }
    
    .chart-analytics-container {
        padding: var(--space-md);
    }
    
    .chart-card {
        padding: var(--space-md);
    }
}

@media (max-width: 480px) {
    .chart-analytics-title {
        font-size: var(--font-size-lg);
    }
    
    .chart-card-header h4 {
        font-size: var(--font-size-md);
    }
    
    .chart-container {
        height: 200px;
    }

}

/* Hours Editor Styles */
.hours-edit-btn {
    position: absolute;
    top: 50%;
    right: 8px;
    transform: translateY(-50%);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 10px;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
    z-index: 10;
}

.hours-edit-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-50%) scale(1.1);
}

.hours-editor {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--space-md);
    margin-top: var(--space-sm);
    box-shadow: var(--shadow-md);
}

.hours-inputs {
    display: flex;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
}

.time-input-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
    flex: 1;
}

.time-input-group label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

.time-input {
    padding: var(--space-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    background: var(--bg-primary);
    transition: border-color var(--transition-fast);
}

.time-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.hours-editor-actions {
    display: flex;
    gap: var(--space-sm);
    justify-content: flex-end;
}

.btn-save-hours,
.btn-cancel-hours {
    padding: var(--space-xs) var(--space-md);
    border: none;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-save-hours {
    background: var(--primary-color);
    color: white;
}

.btn-save-hours:hover {
    background: var(--primary-hover);
}

.btn-cancel-hours {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-cancel-hours:hover {
    background: var(--surface-hover);
    border-color: var(--border-hover);
}

/* Dark mode adjustments for hours editor */
[data-theme="dark"] .time-input {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .time-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.1);
}
