{% if certificates %}
    {% for certificate in certificates %}
        <div class="certificate-list-item" data-certificate-id="{{ certificate.id }}">
            <div class="certificate-icon">
                {% if certificate.is_image %}
                    <div class="file-icon image-icon">
                        <i class="fas fa-image"></i>
                    </div>
                {% elif certificate.is_pdf %}
                    <div class="file-icon pdf-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                {% else %}
                    <div class="file-icon other-icon">
                        <i class="fas fa-file"></i>
                    </div>
                {% endif %}
            </div>

            <div class="certificate-details">
                <div class="certificate-header">
                    <h5 class="certificate-title">{{ certificate.title }}</h5>
                    <div class="certificate-badges">
                        {% if certificate.is_image %}
                            <span class="file-type-badge image">
                                <i class="fas fa-image"></i> Image
                            </span>
                        {% elif certificate.is_pdf %}
                            <span class="file-type-badge pdf">
                                <i class="fas fa-file-pdf"></i> PDF
                            </span>
                        {% else %}
                            <span class="file-type-badge other">
                                <i class="fas fa-file"></i> File
                            </span>
                        {% endif %}

                        <span class="status-badge {{ certificate.is_seen|yesno:'seen,unseen' }}">
                            <i class="fas fa-{{ certificate.is_seen|yesno:'check-circle,exclamation-circle' }}"></i>
                            {{ certificate.is_seen|yesno:'Reviewed,Pending Review' }}
                        </span>
                    </div>
                </div>

                <div class="certificate-meta">
                    <span class="upload-info">
                        <i class="fas fa-calendar"></i>
                        {{ certificate.created_at|date:"M d, Y" }}
                    </span>
                    <span class="uploaded-by">
                        <i class="fas fa-user"></i>
                        {{ certificate.uploaded_by.firstname }} {{ certificate.uploaded_by.lastname }}
                    </span>
                </div>
            </div>

            <div class="certificate-actions">
                <button class="btn btn-primary btn-sm view-certificate" data-certificate-id="{{ certificate.id }}" title="View Certificate">
                    <i class="fas fa-eye"></i>
                    <span>View</span>
                </button>
                <button class="btn btn-accent btn-sm edit-certificate" data-certificate-id="{{ certificate.id }}" title="Edit Certificate">
                    <i class="fas fa-edit"></i>
                    <span>Edit</span>
                </button>
                <button class="btn btn-error btn-sm delete-certificate" data-certificate-id="{{ certificate.id }}" title="Delete Certificate">
                    <i class="fas fa-trash"></i>
                    <span>Delete</span>
                </button>
            </div>
        </div>
    {% endfor %}
{% else %}
    <div class="no-certificates-list">
        <div class="no-certificates-icon">
            <i class="fas fa-certificate"></i>
        </div>
        <span>No certificates found for this employee.</span>
    </div>
{% endif %}
