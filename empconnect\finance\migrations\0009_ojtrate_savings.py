# Generated by Django 5.0.3 on 2025-07-30 02:52

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('finance', '0008_rename_amount_loan_current_balance_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='OJTRate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site', models.CharField(max_length=50, unique=True)),
                ('allowance_day', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('reg_nd_rate', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('reg_nd_ot_rate', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('reg_ot_rate', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('rest_ot_rate', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('legal_rate', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('sat_off_rate', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Savings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('is_withdrawn', models.BooleanField(default=False)),
                ('withdrawal_date', models.DateTimeField(blank=True, null=True)),
                ('deposit_date', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='savings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
