# Generated by Django 5.0.3 on 2025-07-22 07:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('finance', '0005_alter_payslip_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='payslip',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payslips', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='payslip',
            name='file_path',
            field=models.FileField(upload_to='payslips/'),
        ),
        migrations.AlterField(
            model_name='payslip',
            name='uploaded_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_payslips', to=settings.AUTH_USER_MODEL),
        ),
    ]
