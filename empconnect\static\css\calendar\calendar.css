.calendar-layout {
    display: flex;
    gap: var(--space-lg);
    /* background: var(--surface); */
    /* border-radius: var(--radius-lg); */
    /* padding: var(--space-lg); */
    /* box-shadow: var(--shadow-md); */
    max-width: 100%;
    min-height: 100%;
}

.calendar-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--surface);
    border-radius: var(--radius-lg);
    padding: var(--space-md);
    box-shadow: var(--shadow-md);
}

.calendar-controls {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
}

.controls-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.calendar-navigation-row {
    display: flex;
    justify-content: center;
    width: 100%;
}

.view-toggles {
    display: flex;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--space-xs);
    gap: var(--space-xs);
}

.view-toggle {
    padding: var(--space-sm) var(--space-md);
    border: none;
    background: transparent;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-normal);
    min-width: 60px;
}

.view-toggle:hover {
    color: var(--text-primary);
    background: var(--surface-hover);
}

.view-toggle.active {
    background: var(--primary-color);
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
}

.calendar-navigation {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.calendar-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    min-width: 140px;
    text-align: center;
}

.calendar-legend {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-lg);
    margin-bottom: var(--space-sm);
    padding: var(--space-md);
    /* background: var(--bg-secondary); */
    border-radius: var(--radius-md);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--surface);
    box-shadow: var(--shadow-sm);
}

.legend-color.legal-holiday {
    background: #ef4444;
}

.legend-color.special-holiday {
    background: #f59e0b;
}

.legend-color.day-off {
    background: #10b981;
}

.legend-color.company-holiday {
    background: #6366f1;
}

.calendar-grid {
    border-radius: var(--radius-lg);
    overflow: hidden;
    border: 1px solid var(--border-color);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: var(--primary-color);
    color: var(--text-inverse);
    gap: var(--space-sm);
    padding: var(--space-sm);
}

.weekday {
    padding: var(--space-md);
    text-align: center;
    font-weight: 600;
    font-size: var(--font-size-sm);
    border-radius: var(--radius-md);
    /* background: rgba(255, 255, 255, 0.1); */
}

.weekday:last-child {
    border-right: none;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: var(--surface);
    flex: 1;
    gap: var(--space-sm);
    padding: var(--space-sm);
}

.calendar-day {
    min-height: 80px;
    padding: var(--space-sm);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    position: relative;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
    background: var(--surface);
    height: 100%;
    align-items: center;
    justify-content: center;
}

.calendar-day.empty {
    background: var(--bg-tertiary);
    cursor: default;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
}

.calendar-day:not(.empty):hover {
    background: var(--surface-hover);
    transform: scale(1.02);
    z-index: 2;
    box-shadow: var(--shadow-md);
}

.calendar-day.today {
    background: none;
}

.calendar-day.today .day-number {
    background: var(--primary-color);
    color: #fff;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-weight: bold;
}

.calendar-day.selected {
    /* Remove background and box-shadow for selected cell */
    background: none;
    color: inherit;
    box-shadow: none;
}

.calendar-day.selected .day-number,
.day-number.selected-text {
    color: var(--primary-color);
    font-weight: bold;
    background: none;
}

.calendar-day.has-holiday {
    border-color: var(--primary-color);
}

.calendar-day.has-holiday.legal {
    border-color: #ef4444;
}

.calendar-day.has-holiday.legal .day-number {
    color: #ef4444;
}

.calendar-day.has-holiday.special {
    border-color: #f59e0b;
}

.calendar-day.has-holiday.special .day-number {
    color: #f59e0b;
}

.calendar-day.has-holiday.day_off {
    border-color: #10b981;
}

.calendar-day.has-holiday.day_off .day-number {
    color: #10b981;
}

.calendar-day.has-holiday.company {
    border-color: #6366f1;
}

.calendar-day.has-holiday.company .day-number {
    color: #6366f1;
}

.day-number {
    font-size: var(--font-size-md);
    font-weight: 500;
    color: var(--text-primary);
    text-align: center;
}

.holiday-indicators {
    display: none;
}

.holiday-indicator {
    display: none;
}

.calendar-sidebar {
    width: 500px;
    display: flex;
    flex-direction: column;
    border-radius: var(--radius-lg);
    gap: var(--space-lg);
    background: var(--surface);
    padding: var(--space-md);
    box-shadow: var(--shadow-md);
}

.selected-date-display {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md);
    background: var(--surface);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}

.date-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.date-details {
    display: flex;
    flex-direction: column;
}

.date-month {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1;
}

.date-weekday {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sidebar-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.tab-list {
    display: flex;
    background: var(--surface);
    border-radius: var(--radius-md);
    padding: var(--space-xs);
    margin-bottom: var(--space-md);
    box-shadow: var(--shadow-sm);
}

.tab {
    flex: 1;
    background: transparent;
    border: none;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-sm);
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: var(--font-size-sm);
    text-align: center;
    position: relative;
}

.tab:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.tab.active {
    background: var(--primary-color);
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
}

.tab-panels {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.tab-panel {
    display: none;
    flex: 1;
    background: var(--surface);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.tab-panel.active {
    display: flex;
    flex-direction: column;
}

.panel-content {
    flex: 1;
    padding: var(--space-lg);
    display: flex;
    flex-direction: column;
}

.no-data-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    flex: 1;
    min-height: 200px;
    color: var(--text-muted);
}

.no-data-icon {
    font-size: 2.5rem;
    margin-bottom: var(--space-md);
    opacity: 0.5;
}

.no-data-text h4 {
    margin: 0 0 var(--space-sm) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-md);
}

.no-data-text p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.sidebar-actions {
    padding-top: var(--space-md);
    border-top: 1px solid var(--border-color);
}

.add-event-btn {
    width: 100%;
    justify-content: center;
}

.holiday-tooltip {
    position: fixed;
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 0;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.8);
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    z-index: 1000;
    max-width: 300px;
    pointer-events: none;
}

.holiday-tooltip.show {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.tooltip-content {
    padding: var(--space-md);
}

.tooltip-header h4 {
    margin: 0 0 var(--space-sm) 0;
    color: var(--text-primary);
    font-size: var(--font-size-md);
    font-weight: 600;
}

.tooltip-body {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.holiday-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
}

.holiday-type-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.holiday-type-dot.legal {
    background: #ef4444;
}

.holiday-type-dot.special {
    background: #f59e0b;
}

.holiday-type-dot.day_off {
    background: #10b981;
}

.holiday-type-dot.company {
    background: #6366f1;
}

.holiday-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.holiday-description {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-top: 2px;
}

@keyframes holidayAppear {
    from {
        opacity: 0;
        transform: scaleX(0);
    }
    to {
        opacity: 1;
        transform: scaleX(1);
    }
}

.events-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.event-item {
    display: flex;
    align-items: flex-start;
    gap: var(--space-sm);
    padding: var(--space-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
    transition: all var(--transition-normal);
}

.event-item:hover {
    background: var(--surface-hover);
    transform: translateX(2px);
}

.event-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
    margin-top: 2px;
}

.event-indicator.legal {
    background: #ef4444;
}

.event-indicator.special {
    background: #f59e0b;
}

.event-indicator.day_off {
    background: #10b981;
}

.event-indicator.company {
    background: #6366f1;
}

.event-content {
    flex: 1;
}

.event-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-md);
    margin-bottom: 4px;
}

.event-description {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.event-type {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.time-logs-placeholder,
.todos-placeholder {
    text-align: center;
    padding: var(--space-xl);
    color: var(--text-muted);
    font-style: italic;
}

.timelog-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    min-height: 18px;
    z-index: 2;
    background: none;
}
.timelog-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-bottom: 2px;
}
.timelog-dot-blue {
    background: #2563eb;
}
.timelog-dot-red {
    background: #ef4444;
}
.timelog-warning-icon {
    color: #ef4444;
    font-size: 1.2em;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}
.pr-form-btn {
    margin-top: 2px;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #ef4444;
    background: transparent;
    color: #ef4444;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
}
.pr-form-btn:hover {
    background: #ef4444;
    color: #fff;
}

.timelog-tab-dot {
    position: absolute;
    top: -7px;
    right: -7px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #ef4444;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    font-weight: bold;
    box-shadow: 0 2px 6px rgba(0,0,0,0.12);
    z-index: 3;
    pointer-events: none;
}
.timelog-tab-dot .fa-exclamation {
    color: #fff;
    font-size: 0.8rem;
    font-weight: bold;
    line-height: 1;
}

.timelog-details {
    display: flex;
    flex-direction: column;
    gap: 6px;
    font-size: 1.05rem;
    margin-bottom: 8px;
}
.timelog-missing {
    color: #ef4444;
    font-weight: bold;
    margin-left: 4px;
}

.timelog-card {
    display: flex;
    align-items: flex-start;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-xs);
    padding: var(--space-md);
    gap: var(--space-lg);
    margin-bottom: var(--space-sm);
    min-height: 70px;
    border-left: 4px solid var(--primary-color);
}
.timelog-card-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #fff;
    flex-shrink: 0;
}
.timelog-card-complete {
    background: #2563eb;
}
.timelog-card-incomplete {
    background: #ef4444;
}
.timelog-card-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 1.05rem;
}

@media (max-width: 1024px) {
    .calendar-layout {
        flex-direction: column;
        gap: var(--space-md);
    }

    .calendar-main {
        width: 100%;
    }

    .calendar-sidebar {
        width: 100%;
        min-width: unset;
        margin-top: 0;
    }

    .selected-date-display {
        padding: var(--space-md);
    }

    .calendar-controls {
        flex-direction: column;
        align-items: center;
    }

    .view-toggles {
        /* width: 100%; */
        justify-content: center;
    }

    .calendar-navigation {
        margin-top: var(--space-sm);
    }
}

@media (max-width: 768px) {
    .calendar-layout {
        padding: 0;
    }

    .calendar-sidebar {
        border-radius: var(--radius-lg);
        margin-top: var(--space-md);
    }

    .calendar-title {
        font-size: var(--font-size-lg);
        min-width: 120px;
    }

    .calendar-legend {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--space-md);
        padding: var(--space-sm);
    }

    .calendar-day {
        min-height: 60px;
        padding: var(--space-xs);
    }

    .day-number {
        font-size: var(--font-size-sm);
    }

    .weekday {
        padding: var(--space-sm);
        font-size: var(--font-size-xs);
    }

    .holiday-tooltip {
        max-width: 280px;
    }

    .date-number {
        font-size: 2.5rem;
    }

    .date-month {
        font-size: var(--font-size-md);
    }

    .event-title {
        font-size: var(--font-size-sm);
    }

    .event-description {
        font-size: var(--font-size-xs);
    }

    .no-data-icon {
        font-size: 2rem;
    }

    .no-data-text h4 {
        font-size: var(--font-size-md);
    }

    .no-data-text p {
        font-size: var(--font-size-sm);
    }

    .panel-content {
        padding: var(--space-md);
    }

    .controls-row {
        width: 100%;
    }

}

@media (max-width: 480px) {

    .calendar-day {
        min-height: 50px;
    }

    .calendar-legend {
        flex-direction: column;
        align-items: flex-start;
        padding: var(--space-xs);
    }

    .legend-item {
        font-size: var(--font-size-xs);
    }

    .view-toggles {
        flex-wrap: wrap;
    }

    .view-toggle {
        min-width: 45px;
        padding: var(--space-xs) var(--space-sm);
    }

    .selected-date-display {
        padding: var(--space-sm);
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .tab {
        padding: var(--space-sm);
        font-size: var(--font-size-sm);
    }

    .event-title {
        font-size: var(--font-size-sm);
    }

    .event-description {
        font-size: var(--font-size-xs);
    }

    .no-data-icon {
        font-size: 1.5rem;
    }

    .no-data-text h4 {
        font-size: var(--font-size-sm);
    }

    .no-data-text p {
        font-size: var(--font-size-xs);
    }

    .panel-content {
        padding: var(--space-sm);
    }
}

.event-card {
    display: flex;
    align-items: start;
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-xs);
    padding: var(--space-md);
    gap: var(--space-lg);
    margin-bottom: var(--space-sm);
    min-height: 80px;
    border-left: none !important;
}

.event-avatar {
    width: 56px;
    height: 56px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--text-inverse);
    flex-shrink: 0;
}

.event-avatar.legal {
    background: #ef4444;
}
.event-avatar.special {
    background: #f59e0b;
}
.event-avatar.day_off {
    background: #10b981;
}
.event-avatar.company {
    background: #6366f1;
}

.event-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.event-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-md);
    margin-bottom: 4px;
}

.event-description {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

[data-theme="dark"] .event-card {
    background: transparent !important;
}

[data-theme="dark"] .event-title {
    color: var(--text-primary);
}

[data-theme="dark"] .event-description {
    color: var(--text-secondary);
}

.event-actions {
    display: flex;
    height: 100%;
    align-items: start;
    gap: var(--space-md);
    margin-left: var(--space-md);
}

.icon-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1rem;
    cursor: pointer;
    padding: 2px;
    border-radius: 50%;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
}

.icon-btn:hover {
    background: var(--surface-hover);
    color: var(--primary-color);
}

.image-dropzone {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--space-lg);
    text-align: center;
    color: var(--text-secondary);
    background: var(--bg-tertiary);
    cursor: pointer;
    transition: border-color 0.2s, background 0.2s;
    position: relative;
}
.image-dropzone.dragover {
    border-color: var(--primary-color);
    background: var(--primary-light);
}
#holiday-image-preview {
    margin-top: 10px;
    max-width: 100%;
    border-radius: 8px;
    display: block;
}

.pulsing-missing {
    animation: pulse-missing 1.2s infinite;
}

@keyframes pulse-missing {
    0% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); }
    100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0); }
}

.expand-btn {
    display: flex;
    align-items: center;
    gap: 0.4em;
    background: #fff;
    border: 1px solid #d1d5db;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    padding: 0;
    transition: width 0.18s, border-radius 0.18s, box-shadow 0.18s;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    cursor: pointer;
    position: relative;
}
.expand-btn .expand-icon {
    font-size: 1.15em;
    color: #2563eb;
    min-width: 20px;
    min-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.expand-btn .expand-text {
    opacity: 0;
    white-space: nowrap;
    margin-left: 0;
    font-weight: 500;
    font-size: 0.98em;
    color: #374151;
    transition: opacity 0.18s, margin-left 0.18s;
}
.expand-btn:hover, .expand-btn:focus {
    width: 120px;
    border-radius: 20px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.10);
}
.expand-btn:hover .expand-text, .expand-btn:focus .expand-text {
    opacity: 1;
    margin-left: 0.5em;
}

[data-theme="dark"] .event-card.legal,
[data-theme="dark"] .event-card.special,
[data-theme="dark"] .event-card.day_off,
[data-theme="dark"] .event-card.company {
    background: transparent !important;
    border: none !important;
}
[data-theme="dark"] .event-card.legal {
    border: 2px solid #ef4444 !important;
}
[data-theme="dark"] .event-card.special {
    border: 2px solid #f59e0b !important;
}
[data-theme="dark"] .event-card.day_off {
    border: 2px solid #10b981 !important;
}
[data-theme="dark"] .event-card.company {
    border: 2px solid #6366f1 !important;
}

[data-theme="dark"] .event-title,
[data-theme="dark"] .event-description {
    color: #fff !important;
}