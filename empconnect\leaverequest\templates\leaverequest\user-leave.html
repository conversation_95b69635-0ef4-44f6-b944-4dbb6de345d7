{% extends "main.html" %}
{% load static %}

{% block title %}REPConnect - Leave Management{% endblock title %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'css/leave/leave.css' %}">
{% endblock %}

{% block content %}
<div class="page-content" id="page-content">
    <header class="page-header">
        <div class="page-header-content">
            <h2>Leave Management</h2>
            <p>Manage your leave requests and track balances</p>
        </div>
        <div class="page-actions">
            <button class="btn btn-primary" data-action="apply-leave">
                <i class="fas fa-plus"></i>
                Apply for Leave
            </button>
        </div>
    </header>

    <!-- Main Content Tabs -->
    <div class="leave-section">
        <div class="tabs-horizontal" data-tour="tabs-section">
            <div class="tab-list">
                <button class="tab active" data-target="overview">Overview</button>
                <button class="tab" data-target="my-requests">My Requests</button>
                {% if is_approver %}
                    <button class="tab" data-target="approvals">
                        Pending Approvals
                        {% if pending_approvals %}
                        <span class="notification-badge">{{ pending_approvals|length }}</span>
                        {% endif %}
                    </button>
                {% endif %}
            </div>
        </div>

        <div id="overview" class="tab-panel active">
            <!-- Chart and Graph -->
            <div class="overview-two-column">
                    <div class="chart-grid">
                        <div class="chart-card">
                            <div class="chart-card-header">
                                <h4>Status Distribution</h4>
                                <span class="chart-subtitle" id="fiscalYearDuration">Fiscal Year: Loading...</span>
                            </div>
                            <div class="chart-container">
                                <canvas id="statusChart"></canvas>
                            </div>
                        </div>
                        <div class="chart-card">
                            <div class="chart-card-header">
                                <h4>Leave Trend</h4>
                                <span class="chart-subtitle" id="fiscalYearDuration2">Fiscal Year: Loading...</span>
                            </div>
                            <div class="chart-container">
                                <canvas id="leaveTypesChart"></canvas>
                            </div>
                        </div>
                    </div>
            </div>

            <!-- Totals, Calendar and Activities -->
            <div class="overview-two-column">
                    <div class="overview-left-column">
                        <div class="leave-balance-summary">
                            {% if request.user.employment_info.employment_type == 'Regular' and leave_balance_sets %}
                                {% for set in leave_balance_sets %}
                                    <div class="balance-set-card">
                                        <div class="balance-set-card-header">
                                            <div class="balance-period-info">
                                                <span class="balance-period-text">
                                                    {{ set.valid_from|date:"M d, Y" }} - {{ set.valid_to|date:"M d, Y" }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="balance-set-card-body">
                                            <div class="balance-list">
                                                {% for balance in set.balances %}
                                                <div class="leave-balance-card">
                                                    <div class="leave-balance-icon">
                                                        {% if balance.leave_type.code == 'SL' %}
                                                            <div class="leave-balance-icon-circle green">
                                                                <i class="fas fa-thermometer-half"></i>
                                                            </div>   
                                                        {% elif balance.leave_type.code == 'VL' %}
                                                            <div class="leave-balance-icon-circle blue">
                                                                <i class="fas fa-plane"></i>
                                                            </div> 
                                                        {% elif balance.leave_type.code == 'EL' %}
                                                            <div class="leave-balance-icon-circle orange">
                                                                <i class="fas fa-briefcase"></i>
                                                            </div>        
                                                        {% else %}
                                                            <div class="leave-balance-icon-circle red">
                                                                <i class="fas fa-calendar"></i>
                                                            </div>                          
                                                        {% endif %}
                                                    </div>
                                                    <div class="leave-balance-main-row">
                                                        <div class="leave-balance-info">
                                                            <span class="leave-balance-title">{{ balance.leave_type.name }}</span>
                                                            <span class="leave-balance-id">({{ balance.leave_type.code }})</span>
                                                        </div>
                                                        <div class="leave-balance-info-row">
                                                            <span class="leave-balance-remaining"><i class="fas fa-gift"></i> {{ balance.entitled|floatformat:2 }}</span>
                                                            <span class="leave-balance-remaining"><i class="fas fa-minus-circle"></i> {{ balance.used|floatformat:2 }}</span>
                                                            <span class="leave-balance-remaining"><i class="fas fa-check-circle"></i> {{ balance.remaining|floatformat:2 }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div class="balance-summary-card balance-default">
                                    <div class="balance-icon">
                                        <i class="fas fa-calendar-times"></i>
                                    </div>
                                    <div class="balance-info">
                                        <div class="balance-count">0 days per year</div>
                                        <div class="balance-status-text">In the contract</div>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="overview-right-column">
                        <div class="leave-calendar-container">
                            <!-- Calendar Header -->
                            <div class="calendar-header">
                                <div class="calendar-view-controls">
                                    <button class="btn btn-outline" onclick="goToToday()">Today</button>
                                </div>
                                <div class="calendar-date-range">
                                    <button class="btn btn-outline" data-nav="prev" onclick="changeMonth(-1)">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <span class="date-range-text" id="currentMonth">November 2024</span>
                                    <button class="btn btn-outline" data-nav="next" onclick="changeMonth(1)">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Calendar Grid -->
                            <div class="calendar-grid">
                                <div class="calendar-weekdays">
                                    <div class="weekday">Sun</div>
                                    <div class="weekday">Mon</div>
                                    <div class="weekday">Tue</div>
                                    <div class="weekday">Wed</div>
                                    <div class="weekday">Thu</div>
                                    <div class="weekday">Fri</div>
                                    <div class="weekday">Sat</div>
                                </div>
                                <div class="calendar-days" id="calendar-days">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overview-left-column">
                        <div class="recent-activities">
                            <h4>Recent Activities</h4>
                            <div class="activity-list">
                                {% if recent_leaves %}
                                    {% for leave in recent_leaves %}
                                    <div class="activity-item">
                                        <div class="activity-icon status-{{ leave.status }}">
                                            {% if leave.status == 'approved' %}
                                                <i class="fas fa-check"></i>
                                            {% elif leave.status == 'disapproved' %}
                                                <i class="fas fa-times"></i>
                                            {% elif leave.status == 'routing' %}
                                                <i class="fas fa-clock"></i>
                                            {% else %}
                                                <i class="fas fa-calendar"></i>
                                            {% endif %}
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">
                                                {% if leave.status == 'approved' %}
                                                    Leave request approved
                                                {% elif leave.status == 'disapproved' %}
                                                    Leave request disapproved
                                                {% elif leave.status == 'routing' %}
                                                    Leave request pending approval
                                                {% else %}
                                                    Leave request {{ leave.get_status_display|lower }}
                                                {% endif %}
                                            </div>
                                            <div class="activity-meta">
                                                {{ leave.leave_type.name }} • {{ leave.duration_display }} • {{ leave.date_prepared|date:"M d, Y" }}
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="empty-state">
                                        <i class="fas fa-inbox"></i>
                                        <h4>No recent activities</h4>
                                        <p>Apply for leave to see activities here</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
            </div>
        </div>

        <div id="my-requests" class="tab-panel">
            <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Control Number</th>
                                <th>Date Prepared</th>
                                <th>Leave Type</th>
                                <th>Duration</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="myRequestsTable">
                            {% for leave in recent_leaves %}
                            <tr>
                                <td>
                                    <span class="control-number">{{ leave.control_number }}</span>
                                </td>
                                <td>{{ leave.date_prepared|date:"M d, Y" }}</td>
                                <td>{{ leave.leave_type.name }}</td>
                                <td>{{ leave.duration_display }}</td>
                                <td>
                                    <span class="status-badge status-{{ leave.status }}">
                                        {{ leave.get_status_display }}
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline" data-action="view-details" data-control-number="{{ leave.control_number }}" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if leave.status == 'routing' %}
                                        <a href="{% url 'edit' leave.control_number %}" class="btn btn-sm btn-accent" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-error" data-action="cancel-leave" data-control-number="{{ leave.control_number }}" title="Cancel">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center">
                                    <div class="empty-state">
                                        <i class="fas fa-calendar-alt"></i>
                                        <p>No leave requests found.</p>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
            </div>
            <div class="table-actions">
                    <a href="{% url 'requests_list' %}" class="btn btn-outline">
                        <i class="fas fa-list"></i>
                        View All Requests
                    </a>
            </div>
        </div>

        {% if is_approver %}
        <div id="approvals" class="tab-panel">
            <div class="card">
                    <div class="card-header">
                        <h3>
                            <i class="fas fa-tasks"></i>
                            Pending Approvals
                        </h3>
                        <span class="approval-count">{{ pending_approvals|length }} pending</span>
                    </div>
                    <div class="card-body">
                        {% if pending_approvals %}
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Control Number</th>
                                        <th>Employee</th>
                                        <th>Leave Type</th>
                                        <th>Duration</th>
                                        <th>Date Submitted</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for approval in pending_approvals %}
                                    <tr>
                                        <td>
                                            <span class="control-number">{{ approval.control_number }}</span>
                                        </td>
                                        <td>
                                            <div class="employee-info">
                                                <span class="employee-name">{{ approval.employee.full_name }}</span>
                                                <small class="employee-id">{{ approval.employee.idnumber }}</small>
                                            </div>
                                        </td>
                                        <td>{{ approval.leave_type.name }}</td>
                                        <td>{{ approval.duration_display }}</td>
                                        <td>{{ approval.date_prepared|date:"M d, Y" }}</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-primary" data-action="open-approval" data-control-number="{{ approval.control_number }}" title="Review & Approve">
                                                    <i class="fas fa-check"></i>
                                                    Review
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="empty-state">
                            <i class="fas fa-check-circle"></i>
                            <h5>No Pending Approvals</h5>
                            <p>All leave requests have been processed.</p>
                        </div>
                        {% endif %}
                    </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Apply Leave Modal -->
<div id="applyLeaveModal" class="modal modal-md">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h3>Apply for Leave</h3>
            <button class="modal-close" data-action="close-modal" data-modal="applyLeaveModal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="applyLeaveForm" method="post" action="{% url 'apply' %}" enctype="multipart/form-data">
            {% csrf_token %}
            <div class="modal-body">
                <div class="form-row form-row-2col">
                    <div class="form-group">
                        <label for="id_date_from">Date From *</label>
                        <input type="date" name="date_from" id="id_date_from" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="id_date_to">Date To *</label>
                        <input type="date" name="date_to" id="id_date_to" class="form-control" required>
                    </div>
                </div>

                <div class="form-row days-hidden">
                    <div class="form-group">
                        <div id="daysRequested" class="days-display">
                            <div class="days-info">
                                <span class="days-count" id="daysCount">Select dates to calculate</span>
                                <small class="days-range" id="daysRange"></small>
                            </div>
                            <div class="days-info">
                                <span class="days-count" id="hrsCount">Hours Count</span>
                                <small class="days-range" id="hrsRange">7:00 AM to 4:00 PM</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-row form-row-2col">
                    <div class="form-group">
                        <label for="id_leave_type">Leave Type *</label>
                        <select name="leave_type" id="id_leave_type" class="form-control" required>
                            <option value="">Select Leave Type</option>
                            {% for leave_type in leave_types %}
                            <option value="{{ leave_type.id }}">{{ leave_type.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="id_leave_reason">Reason Category *</label>
                        <select name="leave_reason" id="id_leave_reason" class="form-control" required disabled>
                            <option value="">Select Leave Reason</option>
                            {% for leave_reason in leave_reasons %}
                            <option value="{{ leave_reason.id }}">{{ leave_reason.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="id_reason">Reason for Leave *</label>
                    <textarea name="reason" id="id_reason" class="form-control" rows="4" placeholder="Please provide the reason for your leave request..." required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" data-action="close-modal" data-modal="applyLeaveModal">Cancel</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i>
                    Submit Request
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Leave Details Modal -->
<div id="leaveDetailsModal" class="modal">
    <div class="modal-overlay"></div>
    <div class="modal-content modal-lg">
        <div class="modal-header">
            <h3>
                <i class="fas fa-file-alt"></i>
                Leave Request Details
            </h3>
            <button class="modal-close" data-action="close-modal" data-modal="leaveDetailsModal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body" id="leaveDetailsContent">
            <!-- Content loaded via AJAX -->
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-outline" data-action="close-modal" data-modal="leaveDetailsModal">Close</button>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div id="approvalModal" class="modal">
    <div class="modal-overlay"></div>
    <div class="modal-content modal-lg">
        <div class="modal-header">
            <h3>
                <i class="fas fa-gavel"></i>
                Review Leave Request
            </h3>
            <button class="modal-close" data-action="close-modal" data-modal="approvalModal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body" id="approvalContent">
            <!-- Content loaded via AJAX -->
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-outline" data-action="close-modal" data-modal="approvalModal">Cancel</button>
            <button type="button" class="btn btn-error" data-action="process-approval" data-approval-action="disapprove">
                <i class="fas fa-times"></i>
                Disapprove
            </button>
            <button type="button" class="btn btn-success" data-action="process-approval" data-approval-action="approve">
                <i class="fas fa-check"></i>
                Approve
            </button>
        </div>
    </div>
</div>

{% comment %} <div class="toast-container" id="toast-container"></div> {% endcomment %}

{% endblock content %}

{% block extra_js %}
<script>
    window.leaveCalendarData = [
        {% if recent_leaves %}
        {% for leave in recent_leaves %}
        {% if leave.status == 'approved' %}
        {
            startDate: '{{ leave.date_from|date:"Y-m-d" }}',
            endDate: '{{ leave.date_to|date:"Y-m-d" }}',
            leaveType: '{{ leave.leave_type.name|escapejs }}',
            reason: '{{ leave.reason|escapejs|truncatechars:50 }}',
            code: '{{ leave.leave_type.code|escapejs }}',
            controlNumber: '{{ leave.control_number|escapejs }}'
        },
        {% endif %}
        {% endfor %}
        {% endif %}
    ];
</script>
<script src="{% static 'js/leave/user_leave.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const leaveTypeSelect = document.getElementById('id_leave_type');
        const reasonCategorySelect = document.getElementById('id_leave_reason');

        leaveTypeSelect.addEventListener('change', async function () {
            const selectedLeaveType = this.value;

            // Disable Reason Category while loading
            reasonCategorySelect.disabled = true;
            reasonCategorySelect.innerHTML = '<option value="">Loading...</option>';

            if (selectedLeaveType) {
                try {
                    // Fetch reasons for the selected leave type
                    const response = await fetch(`/leave/api/leave-reasons/${selectedLeaveType}/`);
                    const reasons = await response.json();

                    // Populate Reason Category dropdown
                    reasonCategorySelect.innerHTML = '<option value="">Select Leave Reason</option>';
                    reasons.forEach(reason => {
                        const option = document.createElement('option');
                        option.value = reason.id;
                        option.textContent = reason.reason_text;
                        reasonCategorySelect.appendChild(option);
                    });

                    // Enable Reason Category
                    reasonCategorySelect.disabled = false;
                } catch (error) {
                    console.error('Error fetching reasons:', error);
                    reasonCategorySelect.innerHTML = '<option value="">Error loading reasons</option>';
                }
            } else {
                // Reset Reason Category if no Leave Type is selected
                reasonCategorySelect.innerHTML = '<option value="">Select Leave Reason</option>';
            }
        });

        // Modal close logic
        const applyLeaveModal = document.getElementById('applyLeaveModal');
        const modalOverlay = applyLeaveModal.querySelector('.modal-overlay');
        const closeButtons = applyLeaveModal.querySelectorAll('.modal-close, [data-action="close-modal"]');

        // Prevent overlay click from closing modal
        modalOverlay.addEventListener('click', function (e) {
            e.stopPropagation();
        });

        // Only close modal on explicit close button click
        closeButtons.forEach(btn => {
            btn.addEventListener('click', function () {
                applyLeaveModal.classList.remove('open');
            });
        });
    });
</script>   
{% endblock extra_js %}