{% extends "main.html" %}
{% load static %}

{% block title %}REPConnect - Finance{% endblock title %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'css/finance/finance.css' %}">
    <link rel="stylesheet" href="{% static 'css/finance/user.css' %}">
{% endblock %}

{% block content %}
<div class="page-content" id="page-content">
    <header class="page-header">
        <div class="page-header-content">
            <h2>My Financial Summary</h2>
            <p>View your latest payslips and allowances in one place</p>
        </div>
        <button class="tour-btn" onclick="startUserFinanceTour()">
            <span class="tour-icon"><i class="fa-regular fa-circle-question"></i></span>
            <span class="tour-tooltip">Page Tour</span>
        </button>
    </header>

    {% if request.user.employment_info.employment_type != 'OJT' %}
    <div class="dashboard-stats-container">
        <div class="dashboard-stats" data-tour="dashboard-stats" data-intro="<b>Dashboard Statistics</b><br>View your financial overview including total payslips, allowances, savings, and loans for this month." data-step="1">
            <div class="modern-stats-grid">
                <div class="modern-stat-card">
                    <div class="modern-stat-header">
                        <div class="modern-stat-icon blue">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                    </div>
                    <div class="modern-stat-label">Available Payslip</div>
                    <div class="modern-stat-value">{{ total_payslips }}</div>
                    <div class="modern-stat-change">
                        <span class="modern-stat-change-text">This Month</span>
                    </div>
                </div>
                <div class="modern-stat-card">
                    <div class="modern-stat-header">
                        <div class="modern-stat-icon orange">
                            <i class="fas fa-gift"></i>
                        </div>
                    </div>
                    <div class="modern-stat-label">Total Allowances</div>
                    <div class="modern-stat-value">₱ {{ total_allowances|floatformat:2 }}</div>
                    <div class="modern-stat-change">
                        <span class="modern-stat-change-text">This Month</span>
                    </div>
                </div>
                <div class="modern-stat-card">
                    <div class="modern-stat-header">
                        <div class="modern-stat-icon green">
                            <i class="fas fa-piggy-bank"></i>
                        </div>
                    </div>
                    <div class="modern-stat-label">Total Savings</div>
                    <div class="modern-stat-value">₱ {{ total_savings|floatformat:2 }}</div>
                    <div class="modern-stat-change">
                        <span class="modern-stat-change-text">This Month</span>
                    </div>
                </div>
                <div class="modern-stat-card">
                    <div class="modern-stat-header">
                        <div class="modern-stat-icon red">
                            <i class="fas fa-money-check-alt"></i>
                        </div>
                    </div>
                    <div class="modern-stat-label">Total Loans</div>
                    <div class="modern-stat-value">₱ {{ total_loans|floatformat:2 }}</div>
                    <div class="modern-stat-change">
                        <span class="modern-stat-change-text">This Month</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if request.user.employment_info.employment_type != 'OJT' and loans %}
    <div class="user-finance-summary">
        <!-- Loan Summary Card -->
        <div style="width: 100%;">
                <div class="finance-summary-header">
                        <div class="loans-summary-header-left">
                            <div class="loans-summary-title-row">
                                <h2 class="loans-summary-title">Loan Summary</h2>
                                <span class="loans-summary-date">as of {{ now|date:'M d, Y' }}</span>
                            </div>
                        </div>
                        <div class="loans-summary-header-right" data-tour="loans-toggle" data-intro="<b>Table View Toggle</b><br>Switch between active and all loans to customize your view and focus on what matters most to you." data-step="3">
                            <div class="loans-toggle-row">
                                <span class="toggle-label">Show Only Active Loans</span>
                                <label class="loans-toggle-switch">
                                    <input type="checkbox" id="showActiveLoansOnly" checked data-tour="loans-toggle" data-intro="<b>Table View Toggle</b><br>Switch between active and all loans to customize your view and focus on what matters most to you." data-step="3">
                                    <span class="loans-toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                </div>
                <div class="user-section-content">
                    {% if loans %}
                        <div class="user-finance-table" id="loanTableContainer" style="display: flex; flex-direction: column;">
                            <table class="data-table loan-responsive-table" data-tour="loans-table" data-intro="<b>Loan Summary Table</b><br>View all your active and inactive loans with details like principal balance, monthly payments, and current balance." data-step="2">
                                <thead>
                                    <tr>
                                        <th>Loan</th>
                                        <th>Principal Balance</th>
                                        <th class="loan-hide-mobile">Monthly Payment</th>
                                        <th class="loan-hide-mobile">Payment Progress</th>
                                        <th style="text-align: center !important;">Balance</th>
                                        <th class="loan-hide-mobile" style="text-align: center !important;" >Status</th>
                                        <th style="text-align: center !important;">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                        {% for loan in loans %}
                                        <tr data-active="{{ loan.is_active|yesno:'true,false' }}" data-balance="{{ loan.current_balance|floatformat:2 }}" class="loan-row{% if loan.current_balance|floatformat:2 == '0.00' %} inactive-loan{% else %} active-loan{% endif %}" style="{% if loan.current_balance|floatformat:2 == '0.00' %}display:none;{% endif %}">
                                            <td>
                                                <div>
                                                    <span class="loan-type-name">{{ loan.loan_type.loan_type }}</span><br>
                                                    <small class="loan-date-started"> Started at {{ loan.created_at|date:"M d, Y" }}</small>
                                                </div>
                                            </td>
                                            <td>₱ {{ loan.principal_amount|floatformat:2 }}</td>
                                            <td class="loan-hide-mobile">₱ {{ loan.monthly_deduction|floatformat:2 }}</td>
                                            <td class="loan-hide-mobile" style="text-align: center !important;">
                                                <div class="loan-progress-bar-container">
                                                    <div class="loan-progress-bar-bg">
                                                        <div class="loan-progress-bar-fill" data-width="{{ loan.percent_paid|floatformat:0 }}" style="width:0%">
                                                            <span class="loan-progress-percentage">{{ loan.percent_paid|floatformat:0 }}%</span>
                                                        </div>
                                                    </div>
                                                    <div class="loan-progress-bar-info">
                                                        ₱{{ loan.paid|floatformat:2 }} of ₱{{ loan.principal_amount|floatformat:2 }}
                                                    </div>
                                                </div>
                                            </td>
                                            <td style="text-align: center !important;"><strong style="color: var(--primary-color);">₱{{ loan.current_balance|floatformat:2 }}</strong></td>
                                            <td class="loan-hide-mobile" style="text-align: center !important;">
                                                {% if loan.current_balance|floatformat:2 == '0.00' %}
                                                    <span class="status-badge status-attention">
                                                        <i class="fas fa-check-circle"></i> Inactive
                                                    </span>
                                                {% else %}                                                    
                                                    <span class="status-badge status-connected">
                                                        <i class="fas fa-check-circle"></i> Active
                                                    </span>
                                                {% endif %}
                                            </td>
                                            <td style="text-align: center !important;">
                                                <button class="btn btn-sm btn-icon view-loan-btn" title="View Loan" data-loan-id="{{ loan.id }}" data-tour="view-loan-btn" data-intro="<b>View Loan Details</b><br>Click to view detailed information about this specific loan including payment history and terms." data-step="4">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <div class="empty-icon"><i class="fas fa-file-invoice"></i></div>
                            <div class="empty-title">No Loans Found</div>
                            <div class="empty-desc">No loans have been uploaded for this employee yet.</div>
                        </div>
                    {% endif %}
                </div>
        </div>
    </div>
    {% endif %}

    <div class="user-finance-summary finance-flex-row">
        <!-- Payslips Card (Flexible) -->
        <div class="finance-flex-payslip">
            <div class="finance-summary-header">
                <div class="loans-summary-header-left">
                    <div class="loans-summary-title-row">
                        <h2 class="loans-summary-title">Payslips</h2>
                    </div>
                </div>
            </div>
            <div class="user-section-content">
                {% if payslips %}
                    <div class="user-finance-table" id="payslipsTableContainer" style="display: flex; flex-direction: column;">
                        <table class="data-table" data-tour="payslips-table" data-intro="<b>Payslips Table</b><br>Access all your payslips with cutoff dates, file information, and download/email options." data-step="6">
                            <thead>
                                <tr>
                                    <th>Cutoff Date</th>
                                    <th class="payslip-hide-mobile">File Name</th>
                                    <th class="center-align" style="text-align: center !important;">Status</th>
                                    <th style="text-align: center !important;">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payslip in payslips %}
                                    <tr>
                                        <td>{{ payslip.cutoff_date|date:"F j, Y" }}</td>
                                        <td class="payslip-hide-mobile">
                                            <span class="file-name">{{ payslip.file_path.name }}</span>
                                        </td>
                                        <td class="center-align"  style="text-align: center !important;">
                                            {% if payslip.is_send_to_mail %}
                                                <span class="status-badge status-connected">
                                                    <i class="fas fa-check-circle"></i> Sent to mail
                                                </span>
                                            {% else %}
                                                <span class="status-badge status-importing">
                                                    <i class="fas fa-spinner fa-spin"></i> Not yet sent
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td style="text-align: center !important;">
                                            <button type="button" class="btn btn-icon send-payslip-email-btn"
                                                data-payslip-id="{{ payslip.id }}"
                                                data-employee-id="{{ payslip.employee.id }}"
                                                data-cutoff-date="{{ payslip.cutoff_date }}"
                                                data-user-email="{{ request.user.email }}"
                                                data-work-email="{{ request.user.personal_info.work_email }}"
                                                title="Send Payslip to Email"
                                                data-tour="email-payslip-btn" data-intro="<b>Email Payslip</b><br>Send your payslip directly to your email address for easy access and record keeping." data-step="7">
                                                <i class="fas fa-envelope"></i>
                                            </button>
                                        </td>
                                    </tr>
                                {% empty %}
                                    <tr>
                                        <td colspan="4" style="text-align: center;" class="table-no-data">
                                            <div class="empty-icon"><i class="fas fa-search"></i></div>
                                            <div class="table-no-data-title">No payslip found</div>
                                            <div class="table-no-data-desc">Try adjusting your search or filters.</div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="empty-state">
                        <div class="empty-icon"><i class="fas fa-file-invoice"></i></div>
                        <div class="empty-title">No Payslips Found</div>
                        <div class="empty-desc">No payslips have been uploaded for this employee yet.</div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Allowances Card (Fixed, Card/List Design) -->
        {% if request.user.employment_info.employment_type != 'OJT' and allowances %}
        <div class="finance-flex-fixed" data-tour="allowances-section" data-intro="<b>Allowances Section</b><br>View all your allowances with deposit dates and amounts. This section shows your financial benefits and additional compensation." data-step="9">
            <div class="finance-summary-header">
                <div class="loans-summary-header-left">
                    <div class="loans-summary-title-row">
                        <h2 class="loans-summary-title">Allowances</h2>
                    </div>
                </div>
            </div>
            <div class="user-section-content">
                <div class="user-finance-table">
                    <div class="allowance-list">
                        {% for allowance in allowances %}
                            <div class="allowance-item">
                                <div class="allowance-info-left">
                                    <div class="allowance-type">{{ allowance.allowance_type }}</div>
                                    <div class="allowance-date"><i class="fas fa-wallet allowance-date-icon"></i> Deposited {{ allowance.deposit_date|date:"M j, Y" }}</div>
                                </div>
                                <div class="allowance-amount-right">
                                    <span class="allowance-amount">₱ {{ allowance.amount }}</span>
                                </div>
                            </div>
                        {% empty %}
                            <div class="empty-state">
                                <div class="empty-icon"><i class="fas fa-search"></i></div>
                                <div class="empty-title">No allowance found</div>
                                <div class="empty-desc">Try adjusting your search or filters.</div>
                            </div>
                        {% endfor %}
                    </div>
            </div>
            </div>
        </div>
        {% endif %}

        <!-- Savings Card (Fixed) -->
        {% if request.user.employment_info.employment_type != 'OJT' and savings %}
        <div class="finance-flex-fixed" data-tour="savings-section" data-intro="<b>Savings Section</b><br>Track your personal savings account with current balance and activity status. This shows your financial growth and savings plan progress." data-step="10">
            <div class="finance-summary-header">
                <div class="loans-summary-header-left">
                    <div class="loans-summary-title-row">
                        <h2 class="loans-summary-title">Savings</h2>
                    </div>
                </div>
            </div>
            <div class="user-section-content">
                <div class="user-finance-table">
                    <div class="allowance-list">
                        {% for saving in savings %}
                            <div class="allowance-item">
                                <div class="allowance-info-left">
                                    <div class="allowance-type">Personal Savings
                                        {% if not saving.is_withdrawn %}
                                            <i class="fas fa-check-circle savings-active-icon" style="margin-left:8px;"></i>
                                        {% endif %}
                                    </div>
                                    <div class="allowance-date"><i class="fas fa-wallet allowance-date-icon"></i> {% if saving.is_withdrawn %} Withdrawn {{ saving.withdrawal_date|date:"M j, Y" }}{% else %} Started {{ saving.created_at|date:"M j, Y" }}{% endif %} </div>
                                </div>
                                <div class="allowance-amount-right">
                                    <span class="allowance-amount">₱ {{ saving.amount|floatformat:2 }}</span>
                                </div>
                            </div>
                        {% empty %}
                            <div class="empty-state">
                                <div class="empty-icon"><i class="fas fa-search"></i></div>
                                <div class="empty-title">No allowance found</div>
                                <div class="empty-desc">Try adjusting your search or filters.</div>
                            </div>
                        {% endfor %}
                    </div>
            </div>
        </div>
            
        </div>
        {% endif %}
    </div>
</div>

<!-- Email Selection Modal -->
<div id="emailSelectionModal" class="modal modal-sm">
    <div class="modal-overlay"></div>
    <div class="modal-content" data-tour="email-modal" data-intro="<b>Email Selection Modal</b><br>Choose which email address to send your payslip to - your personal email or work email." data-step="8">
        <div class="modal-header">
            <h3>Select Email Address</h3>
            <button class="modal-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="email-selection-form">
                <form id="email-selection-form">
                    {% csrf_token %}
                    <div class="form-group">
                        <label class="form-label">Choose where to send your payslip:</label>
                        <div class="email-selection-options" id="email-options">
                            {% if request.user.email %}
                            <div class="email-option-card">
                                <input type="radio" name="selected_email" value="{{ request.user.email }}" id="account-email" checked>
                                <label for="account-email" class="email-option-label">
                                    <div class="email-option-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="email-option-info">
                                        <div class="email-option-name">{{ request.user.email }}</div>
                                        <div class="email-option-username">Personal Email Address</div>
                                    </div>
                                    <div class="email-option-check">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                </label>
                            </div>
                            {% endif %}
                            {% if request.user.personal_info.work_email and request.user.personal_info.work_email != request.user.email %}
                            <div class="email-option-card">
                                <input type="radio" name="selected_email" value="{{ request.user.personal_info.work_email }}" id="work-email">
                                <label for="work-email" class="email-option-label">
                                    <div class="email-option-avatar">
                                        <i class="fas fa-briefcase"></i>
                                    </div>
                                    <div class="email-option-info">
                                        <div class="email-option-name">{{ request.user.personal_info.work_email }}</div>
                                        <div class="email-option-username">Work Email Address</div>
                                    </div>
                                    <div class="email-option-check">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                </label>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="payslip-info" id="payslip-info" style="display: none;">
                        <h4>Payslip Details</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="label">Employee:</span>
                                <span class="value" id="payslip-employee-name">-</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Period:</span>
                                <span class="value" id="payslip-period">-</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Type:</span>
                                <span class="value" id="payslip-type">-</span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline">Cancel</button>
            <button class="btn btn-primary" id="confirm-email-send">
                <i class="fas fa-envelope"></i>
                Send Payslip
            </button>
        </div>
    </div>
</div>

<!-- Email Loader -->
<div class="loader-container" id="emailLoader" style="display: none;">
  <div class="payslip-email-loader"></div>
  <p>Sending Payslip.....</p>
</div>

<!-- Loan Deductions Modal -->
<div id="loanDeductionsModal" class="modal modal-md">
    <div class="modal-overlay"></div>
    <div class="modal-content" data-tour="deduction-modal" data-intro="<b>Loan Deductions Modal</b><br>This modal shows detailed information about loan deductions including payment schedules and amounts." data-step="5">
        <div class="modal-header">
            <h3>Loan Deductions</h3>
            <button class="modal-close"><i class="fas fa-times"></i></button>
        </div>
        <div class="modal-body" id="loanDeductionsBody">
            <div class="loader" style="text-align:center; padding:2em;">Loading...</div>
        </div>
    </div>
</div>

{% endblock content %}

{% block extra_js %}
    <script src="{% static 'js/finance/user-finance.js' %}"></script>
{% endblock %}