# Generated by Django 5.0.3 on 2025-08-02 00:23

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('leaverequest', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='leaveapprovaltimeline',
            name='actor',
        ),
        migrations.RemoveField(
            model_name='leaveapprovaltimeline',
            name='leave_request',
        ),
        migrations.RemoveField(
            model_name='leaverequest',
            name='approved_at',
        ),
        migrations.RemoveField(
            model_name='leaverequest',
            name='cancelled_at',
        ),
        migrations.RemoveField(
            model_name='leaverequest',
            name='current_approver',
        ),
        migrations.RemoveField(
            model_name='leaverequest',
            name='disapproved_at',
        ),
        migrations.RemoveField(
            model_name='leaverequest',
            name='final_approver',
        ),
        migrations.CreateModel(
            name='LeaveApprovalAction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sequence', models.PositiveIntegerField()),
                ('status', models.CharField(choices=[('routing', 'Routing'), ('approved', 'Approved'), ('disapproved', 'Disapproved'), ('cancelled', 'Cancelled')], default='routing', max_length=20)),
                ('action', models.CharField(choices=[('submitted', 'Submitted'), ('approved', 'Approved'), ('disapproved', 'Disapproved'), ('cancelled', 'Cancelled'), ('forwarded', 'Forwarded')], max_length=20)),
                ('comments', models.TextField(blank=True)),
                ('action_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('approver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='approval_actions', to=settings.AUTH_USER_MODEL)),
                ('leave_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='approval_actions', to='leaverequest.leaverequest')),
            ],
            options={
                'ordering': ['sequence', 'created_at'],
                'unique_together': {('leave_request', 'approver', 'sequence', 'action')},
            },
        ),
        migrations.CreateModel(
            name='LeaveReason',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason_text', models.CharField(max_length=255)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reasons', to='leaverequest.leavetype')),
            ],
            options={
                'ordering': ['leave_type', 'reason_text'],
            },
        ),
        migrations.DeleteModel(
            name='LeaveApprovalFlow',
        ),
        migrations.DeleteModel(
            name='LeaveApprovalTimeline',
        ),
    ]
